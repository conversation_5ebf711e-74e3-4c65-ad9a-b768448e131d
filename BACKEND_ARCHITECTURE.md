# 🌳 Hopie App - Arquitectura Backend Completa

## 📋 Tabla de Contenidos
1. [Análisis de Funcionalidades](#análisis-de-funcionalidades)
2. [Patrones de Acceso DynamoDB](#patrones-de-acceso-dynamodb)
3. [Arquitectura de Lambdas](#arquitectura-de-lambdas)
4. [Estructura de Datos](#estructura-de-datos)
5. [APIs y Endpoints](#apis-y-endpoints)
6. [Flujos de Trabajo](#flujos-de-trabajo)

---

## 🗄️ Estrategia de Almacenamiento S3

### **📦 Estructura de Buckets**

#### **Bucket 1: `hopie-user-content-{env}`**
**Propósito**: Contenido generado por usuarios
**Estructura de carpetas**:
```
hopie-user-content-prod/
├── avatars/
│   ├── user-{userId}/
│   │   ├── profile.jpg
│   │   └── profile_thumb.jpg
├── couples/
│   └── {coupleId}/
│       ├── places/
│       │   ├── {placeId}/
│       │   │   ├── main.jpg
│       │   │   ├── thumb.jpg
│       │   │   └── gallery/
│       │   │       ├── img1.jpg
│       │   │       └── img2.jpg
│       ├── chat/
│       │   └── attachments/
│       │       ├── {messageId}/
│       │       │   ├── image.jpg
│       │       │   └── thumb.jpg
│       └── lifeplan/
│           └── {stageId}/
│               ├── cover.jpg
│               └── gallery/
│                   ├── progress1.jpg
│                   └── progress2.jpg
```

#### **Bucket 2: `hopie-app-assets-{env}`**
**Propósito**: Assets estáticos de la aplicación
**Estructura de carpetas**:
```
hopie-app-assets-prod/
├── trees/
│   ├── roble/
│   │   ├── semilla.png
│   │   ├── brote.png
│   │   ├── plantula.png
│   │   ├── arbolJoven.png
│   │   ├── arbolMaduro.png
│   │   └── arbolFlorecido.png
│   ├── cerezo/
│   │   ├── semilla.png
│   │   └── ...
│   ├── pino/
│   ├── sauce/
│   └── olivo/
├── backgrounds/
│   ├── tree-backgrounds/
│   │   ├── forest.jpg
│   │   ├── garden.jpg
│   │   └── sunset.jpg
├── icons/
│   ├── place-categories/
│   │   ├── restaurant.svg
│   │   ├── cafe.svg
│   │   ├── park.svg
│   │   └── home.svg
└── ui/
    ├── onboarding/
    └── tutorials/
```

### **🔗 URLs y Rutas en DynamoDB**

#### **Formato de URLs**:
- **User Content**: `https://d123abc.cloudfront.net/couples/{coupleId}/places/{placeId}/main.jpg`
- **App Assets**: `https://d456def.cloudfront.net/trees/roble/plantula.png`

#### **Almacenamiento en DynamoDB**:
```json
{
  "imageUrl": "couples/456/places/789/main.jpg",
  "thumbnailUrl": "couples/456/places/789/thumb.jpg",
  "galleryUrls": [
    "couples/456/places/789/gallery/img1.jpg",
    "couples/456/places/789/gallery/img2.jpg"
  ]
}
```

---

## 🔍 Análisis de Funcionalidades

### 🏠 **Pantalla Principal (TreeScreen)**
- **Saludo dinámico** según hora del día
- **Información de la pareja** (nivel, puntos de experiencia)
- **Árbol principal** con visualización por etapas
- **Pregunta diaria** con respuestas de ambos usuarios
- **Botón de regar** (acción diaria)
- **Progreso y estadísticas**

### 🌱 **Sistema de Árbol**
- **5 tipos de árboles** predefinidos para selección
- **6 etapas de crecimiento**: Semilla → Brote → Plántula → Joven → Maduro → Florecido
- **Sistema de niveles** (0-100+) que determina la etapa visual
- **Mecánica de riego diario** para subir de nivel
- **Historial de riego** y estadísticas

### 💬 **Preguntas Diarias**
- **Pregunta única por día** para ambos usuarios
- **Respuestas individuales** guardadas con timestamp
- **Historial completo** de preguntas y respuestas
- **Sistema de notificaciones** para recordar responder

### 📊 **Plan de Vida**
- **4 etapas predeterminadas** por defecto
- **CRUD completo**: Crear, leer, actualizar, eliminar etapas
- **Estados**: Completado/En progreso
- **Notas personalizadas** por etapa
- **Fechas de inicio y fin**

### 📍 **Lugares Especiales**
- **CRUD de lugares** con coordenadas GPS
- **Categorías**: Casa, Restaurante, Parque, Café, etc.
- **Fotos y descripciones**
- **Visualización en mapa**
- **Historial de visitas**

### 💬 **Chat de Pareja**
- **Mensajes en tiempo real**
- **Historial de conversaciones**
- **Estados de mensaje**: Enviado, entregado, leído
- **Notificaciones push**

### 📍 **Ubicación en Tiempo Real**
- **Compartir ubicación** opcional
- **Historial de ubicaciones**
- **Configuración de privacidad**
- **Notificaciones de proximidad**

### 👥 **Sistema de Usuarios y Parejas**
- **Autenticación** con Cognito
- **Perfiles de usuario**
- **Sistema de invitaciones** para formar pareja
- **Configuraciones de privacidad**

### 📈 **Estadísticas y Rachas**
- **Racha individual** de días consecutivos
- **Estadísticas de riego**
- **Progreso del árbol**
- **Actividad diaria**

---

## 🗄️ Patrones de Acceso DynamoDB

### **Tabla Principal: `hopie-main-table`**

#### **Estructura de Claves:**
- **PK (Partition Key)**: Identifica el tipo de entidad
- **SK (Sort Key)**: Identifica la instancia específica y permite ordenamiento

#### **Patrones de Acceso Identificados:**

### 1. **Gestión de Usuarios**
```
PK: USER#{userId}
SK: PROFILE
- Datos del perfil del usuario
```

### 2. **Gestión de Parejas**
```
PK: COUPLE#{coupleId}
SK: METADATA
- Información de la pareja, nivel, puntos, árbol seleccionado
```

### 3. **Sistema de Árbol**
```
PK: COUPLE#{coupleId}
SK: TREE#PROGRESS
- Nivel actual, etapa, último riego, estadísticas

PK: TREE_TYPE#{treeType}
SK: METADATA
- Información de los 5 tipos de árboles disponibles

PK: COUPLE#{coupleId}
SK: WATERING#{YYYY-MM-DD}#{userId}
- Historial de riego diario por usuario
```

### 4. **Preguntas Diarias**
```
PK: DAILY_QUESTION#{YYYY-MM-DD}
SK: QUESTION
- Pregunta del día

PK: COUPLE#{coupleId}
SK: ANSWER#{YYYY-MM-DD}#{userId}
- Respuestas individuales por fecha
```

### 5. **Plan de Vida**
```
PK: COUPLE#{coupleId}
SK: LIFEPLAN#{stageId}
- Etapas del plan de vida con estado y notas
```

### 6. **Lugares Especiales**
```
PK: COUPLE#{coupleId}
SK: PLACE#{placeId}
- Lugares guardados con coordenadas y detalles
```

### 7. **Chat**
```
PK: COUPLE#{coupleId}
SK: MESSAGE#{timestamp}#{messageId}
- Mensajes del chat ordenados por tiempo
```

### 8. **Ubicaciones**
```
PK: COUPLE#{coupleId}
SK: LOCATION#{userId}#{timestamp}
- Historial de ubicaciones por usuario
```

### 9. **Estadísticas y Rachas**
```
PK: USER#{userId}
SK: STREAK#{YYYY-MM}
- Racha mensual del usuario

PK: COUPLE#{coupleId}
SK: STATS#{YYYY-MM}
- Estadísticas mensuales de la pareja
```

---

## ⚡ Arquitectura de Lambdas

### **🔐 Autenticación y Usuarios**

#### **1. `auth-user-management`**
- **POST** `/auth/register` - Registro de usuario
- **POST** `/auth/login` - Inicio de sesión
- **PUT** `/auth/profile` - Actualizar perfil
- **GET** `/auth/profile` - Obtener perfil

#### **2. `couple-management`**
- **POST** `/couple/invite` - Enviar invitación
- **POST** `/couple/accept` - Aceptar invitación
- **GET** `/couple/info` - Información de la pareja
- **PUT** `/couple/settings` - Configuraciones

### **🌳 Sistema de Árbol**

#### **3. `tree-management`**
- **GET** `/tree/types` - Obtener tipos de árboles disponibles
- **POST** `/tree/select` - Seleccionar tipo de árbol
- **GET** `/tree/progress` - Obtener progreso actual
- **POST** `/tree/water` - Regar árbol (acción diaria)
- **GET** `/tree/history` - Historial de riego

#### **4. `tree-level-processor`**
- **Función interna** - Procesar cambios de nivel y etapa
- **Trigger**: Después de regar el árbol
- **Lógica**: Calcular nueva etapa basada en nivel

### **💬 Preguntas Diarias**

#### **5. `daily-questions`**
- **GET** `/questions/today` - Pregunta del día
- **POST** `/questions/answer` - Guardar respuesta
- **GET** `/questions/history` - Historial de preguntas
- **GET** `/questions/couple-answers/{date}` - Respuestas de la pareja

#### **6. `question-scheduler`**
- **Función programada** - Crear pregunta diaria
- **Trigger**: CloudWatch Events (diario a las 00:00 UTC)
- **Lógica**: Seleccionar pregunta aleatoria del pool

### **📊 Plan de Vida**

#### **7. `life-plan-management`**
- **GET** `/lifeplan/stages` - Obtener etapas
- **POST** `/lifeplan/stage` - Crear nueva etapa
- **PUT** `/lifeplan/stage/{id}` - Actualizar etapa
- **DELETE** `/lifeplan/stage/{id}` - Eliminar etapa
- **POST** `/lifeplan/stage/{id}/complete` - Marcar como completada

### **📍 Lugares Especiales**

#### **8. `places-management`**
- **GET** `/places` - Obtener lugares guardados
- **POST** `/places` - Guardar nuevo lugar
- **PUT** `/places/{id}` - Actualizar lugar
- **DELETE** `/places/{id}` - Eliminar lugar
- **GET** `/places/nearby` - Lugares cercanos

### **💬 Chat en Tiempo Real**

#### **9. `chat-websocket`**
- **WebSocket API** - Conexiones en tiempo real
- **onConnect** - Establecer conexión
- **onDisconnect** - Cerrar conexión
- **sendMessage** - Enviar mensaje
- **getHistory** - Obtener historial

#### **10. `chat-message-processor`**
- **Función interna** - Procesar mensajes
- **Trigger**: DynamoDB Streams
- **Lógica**: Enviar notificaciones push

### **📍 Ubicación**

#### **11. `location-management`**
- **POST** `/location/share` - Compartir ubicación
- **GET** `/location/current` - Ubicación actual de la pareja
- **GET** `/location/history` - Historial de ubicaciones
- **PUT** `/location/settings` - Configurar privacidad

### **📈 Estadísticas**

#### **12. `statistics-processor`**
- **GET** `/stats/streak` - Racha actual
- **GET** `/stats/monthly` - Estadísticas mensuales
- **GET** `/stats/tree-progress` - Progreso del árbol

#### **13. `streak-calculator`**
- **Función programada** - Calcular rachas diarias
- **Trigger**: CloudWatch Events (diario a las 01:00 UTC)
- **Lógica**: Actualizar rachas basadas en actividad

### **🔔 Notificaciones**

#### **14. `notification-service`**
- **Función interna** - Enviar notificaciones
- **Triggers**: Múltiples eventos
- **Lógica**: Push notifications, emails

#### **15. `image-management`**
- **POST** `/images/upload-url` - Generar URLs firmadas para subida
- **POST** `/images/process` - Procesar imágenes (redimensionar, thumbnails)
- **DELETE** `/images/{path}` - Eliminar imágenes
- **GET** `/images/signed-url/{path}` - URLs firmadas para descarga

#### **16. `image-processor`**
- **Función interna** - Procesar imágenes automáticamente
- **Trigger**: S3 Events cuando se sube una imagen
- **Lógica**: Crear thumbnails, redimensionar, optimizar

---

## 📊 Estructura de Datos Detallada

### **Usuario**
```json
{
  "PK": "USER#123",
  "SK": "PROFILE",
  "userId": "123",
  "email": "<EMAIL>",
  "name": "Juan Pérez",
  "avatarUrl": "avatars/user-123/profile.jpg",
  "avatarThumbnailUrl": "avatars/user-123/profile_thumb.jpg",
  "createdAt": "2024-01-01T00:00:00Z",
  "settings": {
    "notifications": true,
    "locationSharing": false
  }
}
```

### **Pareja**
```json
{
  "PK": "COUPLE#456",
  "SK": "METADATA",
  "coupleId": "456",
  "user1Id": "123",
  "user2Id": "124",
  "level": 15,
  "experiencePoints": 1250,
  "treeType": "roble",
  "currentStage": "plantula",
  "createdAt": "2024-01-01T00:00:00Z",
  "lastActivity": "2024-01-15T10:30:00Z"
}
```

### **Progreso del Árbol**
```json
{
  "PK": "COUPLE#456",
  "SK": "TREE#PROGRESS",
  "level": 15,
  "currentStage": "plantula",
  "totalWaterings": 45,
  "lastWatered": "2024-01-15",
  "stageChangedAt": "2024-01-10T00:00:00Z",
  "nextStageAt": 21,
  "experiencePoints": 1250
}
```

### **Tipos de Árboles**
```json
{
  "PK": "TREE_TYPE#roble",
  "SK": "METADATA",
  "name": "Roble",
  "description": "Símbolo de fortaleza y resistencia. Representa una relación sólida que crece lentamente pero con raíces profundas.",
  "spiritualMeaning": "El roble simboliza la sabiduría, la protección y la longevidad en el amor.",
  "stages": {
    "semilla": {
      "minLevel": 0,
      "imageUrl": "trees/roble/semilla.png",
      "backgroundUrl": "backgrounds/tree-backgrounds/forest.jpg"
    },
    "brote": {
      "minLevel": 7,
      "imageUrl": "trees/roble/brote.png",
      "backgroundUrl": "backgrounds/tree-backgrounds/forest.jpg"
    },
    "plantula": {
      "minLevel": 21,
      "imageUrl": "trees/roble/plantula.png",
      "backgroundUrl": "backgrounds/tree-backgrounds/garden.jpg"
    },
    "arbolJoven": {
      "minLevel": 50,
      "imageUrl": "trees/roble/arbolJoven.png",
      "backgroundUrl": "backgrounds/tree-backgrounds/garden.jpg"
    },
    "arbolMaduro": {
      "minLevel": 100,
      "imageUrl": "trees/roble/arbolMaduro.png",
      "backgroundUrl": "backgrounds/tree-backgrounds/sunset.jpg"
    },
    "arbolFlorecido": {
      "minLevel": 200,
      "imageUrl": "trees/roble/arbolFlorecido.png",
      "backgroundUrl": "backgrounds/tree-backgrounds/sunset.jpg"
    }
  }
}
```

### **Riego Diario**
```json
{
  "PK": "COUPLE#456",
  "SK": "WATERING#2024-01-15#123",
  "userId": "123",
  "date": "2024-01-15",
  "timestamp": "2024-01-15T10:30:00Z",
  "experienceGained": 25,
  "levelBefore": 14,
  "levelAfter": 15,
  "stageChanged": true
}
```

### **Pregunta Diaria**
```json
{
  "PK": "DAILY_QUESTION#2024-01-15",
  "SK": "QUESTION",
  "date": "2024-01-15",
  "question": "¿Cuál es tu recuerdo favorito de nosotros?",
  "category": "memories",
  "createdAt": "2024-01-15T00:00:00Z"
}
```

### **Respuesta a Pregunta**
```json
{
  "PK": "COUPLE#456",
  "SK": "ANSWER#2024-01-15#123",
  "userId": "123",
  "date": "2024-01-15",
  "question": "¿Cuál es tu recuerdo favorito de nosotros?",
  "answer": "Nuestro primer viaje juntos a la playa...",
  "answeredAt": "2024-01-15T14:30:00Z"
}
```

### **Etapa del Plan de Vida**
```json
{
  "PK": "COUPLE#456",
  "SK": "LIFEPLAN#001",
  "stageId": "001",
  "title": "Mudarnos juntos",
  "description": "Encontrar un lugar para vivir juntos",
  "status": "completed",
  "notes": "Encontramos el apartamento perfecto en el centro",
  "targetDate": "2024-06-01",
  "completedAt": "2024-05-15T00:00:00Z",
  "createdAt": "2024-01-01T00:00:00Z",
  "order": 1,
  "isDefault": true,
  "coverImageUrl": "couples/456/lifeplan/001/cover.jpg",
  "progressImages": [
    "couples/456/lifeplan/001/gallery/progress1.jpg",
    "couples/456/lifeplan/001/gallery/progress2.jpg"
  ]
}
```

### **Lugar Especial**
```json
{
  "PK": "COUPLE#456",
  "SK": "PLACE#789",
  "placeId": "789",
  "name": "Nuestro primer café",
  "description": "Donde tuvimos nuestra primera cita",
  "category": "cafe",
  "categoryIcon": "icons/place-categories/cafe.svg",
  "latitude": 19.4326,
  "longitude": -99.1332,
  "address": "Calle Principal 123",
  "mainImageUrl": "couples/456/places/789/main.jpg",
  "thumbnailUrl": "couples/456/places/789/thumb.jpg",
  "galleryUrls": [
    "couples/456/places/789/gallery/img1.jpg",
    "couples/456/places/789/gallery/img2.jpg"
  ],
  "visitedAt": "2024-01-01T00:00:00Z",
  "createdAt": "2024-01-02T00:00:00Z",
  "isFavorite": true
}
```

### **Mensaje de Chat**
```json
{
  "PK": "COUPLE#456",
  "SK": "MESSAGE#1705334400000#msg789",
  "messageId": "msg789",
  "senderId": "123",
  "content": "¡Hola amor! ¿Cómo va tu día?",
  "timestamp": 1705334400000,
  "status": "read",
  "readAt": "2024-01-15T15:00:00Z"
}
```

### **Ubicación**
```json
{
  "PK": "COUPLE#456",
  "SK": "LOCATION#123#1705334400000",
  "userId": "123",
  "latitude": 19.4326,
  "longitude": -99.1332,
  "accuracy": 10,
  "timestamp": 1705334400000,
  "address": "Calle Principal 123",
  "isSharing": true
}
```

### **Racha del Usuario**
```json
{
  "PK": "USER#123",
  "SK": "STREAK#2024-01",
  "userId": "123",
  "month": "2024-01",
  "currentStreak": 15,
  "longestStreak": 25,
  "lastActivityDate": "2024-01-15",
  "activeDays": [1,2,3,5,6,7,8,9,10,11,12,13,14,15],
  "totalActiveDays": 14
}
```

---

## 🔗 APIs y Endpoints Completos

### **🔐 Autenticación (`auth-user-management`)**
```
POST   /auth/register
POST   /auth/login
POST   /auth/logout
GET    /auth/profile
PUT    /auth/profile
DELETE /auth/account
POST   /auth/forgot-password
POST   /auth/reset-password
```

### **👥 Gestión de Parejas (`couple-management`)**
```
POST   /couple/invite          # Enviar invitación
POST   /couple/accept          # Aceptar invitación
POST   /couple/reject          # Rechazar invitación
GET    /couple/info            # Información de la pareja
PUT    /couple/settings        # Configuraciones
DELETE /couple/disconnect      # Desconectar pareja
GET    /couple/invitations     # Ver invitaciones pendientes
```

### **🌳 Sistema de Árbol (`tree-management`)**
```
GET    /tree/types             # Tipos de árboles disponibles
POST   /tree/vote              # Votar por tipo de árbol
GET    /tree/votes             # Ver votos actuales
POST   /tree/select            # Confirmar selección
GET    /tree/progress          # Progreso actual
POST   /tree/water             # Regar árbol
GET    /tree/history           # Historial de riego
GET    /tree/can-water         # Verificar si puede regar hoy
GET    /tree/stats             # Estadísticas del árbol
```

### **💬 Preguntas Diarias (`daily-questions`)**
```
GET    /questions/today        # Pregunta del día
POST   /questions/answer       # Guardar respuesta
GET    /questions/history      # Historial de preguntas
GET    /questions/couple-answers/{date}  # Respuestas de la pareja
GET    /questions/unanswered   # Preguntas sin responder
GET    /questions/stats        # Estadísticas de respuestas
```

### **📊 Plan de Vida (`life-plan-management`)**
```
GET    /lifeplan/stages        # Obtener todas las etapas
POST   /lifeplan/stage         # Crear nueva etapa
GET    /lifeplan/stage/{id}    # Obtener etapa específica
PUT    /lifeplan/stage/{id}    # Actualizar etapa
DELETE /lifeplan/stage/{id}    # Eliminar etapa
POST   /lifeplan/stage/{id}/complete    # Marcar como completada
POST   /lifeplan/stage/{id}/uncomplete  # Marcar como pendiente
PUT    /lifeplan/reorder       # Reordenar etapas
```

### **📍 Lugares Especiales (`places-management`)**
```
GET    /places                 # Obtener lugares guardados
POST   /places                 # Guardar nuevo lugar
GET    /places/{id}            # Obtener lugar específico
PUT    /places/{id}            # Actualizar lugar
DELETE /places/{id}            # Eliminar lugar
GET    /places/nearby          # Lugares cercanos
POST   /places/{id}/visit      # Marcar como visitado
GET    /places/categories      # Categorías disponibles
```

### **💬 Chat (`chat-websocket` + `chat-message-processor`)**
```
WebSocket /chat                # Conexión en tiempo real
GET    /chat/history           # Historial de mensajes
POST   /chat/message           # Enviar mensaje (REST fallback)
PUT    /chat/message/{id}/read # Marcar como leído
DELETE /chat/message/{id}      # Eliminar mensaje
GET    /chat/unread-count      # Contador de no leídos
```

### **📍 Ubicación (`location-management`)**
```
POST   /location/share         # Compartir ubicación actual
GET    /location/current       # Ubicación actual de la pareja
GET    /location/history       # Historial de ubicaciones
PUT    /location/settings      # Configurar privacidad
DELETE /location/stop-sharing  # Dejar de compartir
GET    /location/distance      # Distancia entre la pareja
```

### **📈 Estadísticas (`statistics-processor`)**
```
GET    /stats/streak           # Racha actual del usuario
GET    /stats/couple-activity  # Actividad de la pareja
GET    /stats/monthly          # Estadísticas mensuales
GET    /stats/tree-progress    # Progreso del árbol
GET    /stats/questions        # Estadísticas de preguntas
GET    /stats/places           # Estadísticas de lugares
GET    /stats/dashboard        # Dashboard completo
```

### **🔔 Notificaciones (`notification-service`)**
```
GET    /notifications          # Obtener notificaciones
POST   /notifications/register-device  # Registrar dispositivo
PUT    /notifications/settings # Configurar notificaciones
DELETE /notifications/{id}     # Eliminar notificación
POST   /notifications/test     # Enviar notificación de prueba
```

### **📸 Gestión de Imágenes (`image-management`)**
```
POST   /images/upload-url      # Obtener URL firmada para subir
POST   /images/process         # Procesar imagen subida
DELETE /images/{path}          # Eliminar imagen
GET    /images/signed-url/{path}  # URL firmada para descarga
POST   /images/avatar/upload   # Subir avatar de usuario
POST   /images/place/upload    # Subir imagen de lugar
POST   /images/lifeplan/upload # Subir imagen de plan de vida
POST   /images/chat/upload     # Subir imagen en chat
GET    /images/tree/{type}/{stage}  # Obtener imagen de árbol
```

---

## 🔄 Flujos de Trabajo Críticos

### **1. Flujo de Registro y Emparejamiento**
```mermaid
graph TD
    A[Usuario se registra] --> B[Completa perfil]
    B --> C[Envía invitación a pareja]
    C --> D[Pareja recibe notificación]
    D --> E[Pareja acepta invitación]
    E --> F[Se crea registro de pareja]
    F --> G[Selección de árbol]
    G --> H[Votación por árbol]
    H --> I[Confirmación de selección]
    I --> J[Inicialización del árbol]
```

### **2. Flujo de Riego Diario**
```mermaid
graph TD
    A[Usuario hace clic en regar] --> B[Verificar si puede regar hoy]
    B -->|Sí| C[Incrementar experiencia]
    B -->|No| D[Mostrar mensaje de error]
    C --> E[Verificar cambio de nivel]
    E -->|Sí| F[Actualizar etapa del árbol]
    E -->|No| G[Actualizar solo experiencia]
    F --> H[Notificar a la pareja]
    G --> H
    H --> I[Actualizar racha]
```

### **3. Flujo de Pregunta Diaria**
```mermaid
graph TD
    A[Scheduler crea pregunta diaria] --> B[Usuario ve pregunta]
    B --> C[Usuario responde]
    C --> D[Guardar respuesta]
    D --> E[Verificar si ambos respondieron]
    E -->|Sí| F[Notificar que ambos respondieron]
    E -->|No| G[Esperar respuesta de pareja]
    F --> H[Mostrar respuestas de ambos]
```

### **4. Flujo de Chat en Tiempo Real**
```mermaid
graph TD
    A[Usuario envía mensaje] --> B[WebSocket recibe mensaje]
    B --> C[Guardar en DynamoDB]
    C --> D[DynamoDB Stream trigger]
    D --> E[Procesar mensaje]
    E --> F[Enviar a pareja vía WebSocket]
    F --> G[Enviar notificación push]
    G --> H[Actualizar estado a entregado]
```

### **5. Flujo de Subida de Imágenes**
```mermaid
graph TD
    A[Usuario selecciona imagen] --> B[App solicita URL firmada]
    B --> C[Lambda genera URL firmada S3]
    C --> D[App sube imagen directamente a S3]
    D --> E[S3 Event trigger]
    E --> F[Lambda procesa imagen]
    F --> G[Crear thumbnail]
    G --> H[Actualizar DynamoDB con URLs]
    H --> I[Notificar a la app]
```

---

## 🏗️ Arquitectura de Infraestructura

### **Servicios AWS Requeridos:**

1. **API Gateway** - REST APIs y WebSocket
2. **Lambda Functions** - 14 funciones principales
3. **DynamoDB** - Base de datos principal
4. **Cognito** - Autenticación de usuarios
5. **S3 Buckets** - Almacenamiento de imágenes (2 buckets)
6. **CloudWatch Events** - Tareas programadas
7. **SNS** - Notificaciones push
8. **CloudFront** - CDN para imágenes
9. **DynamoDB Streams** - Triggers en tiempo real

---

## 💰 ANÁLISIS DE OPTIMIZACIÓN DE COSTOS

### **🔍 Análisis Crítico de la Arquitectura Actual**

#### **❌ Problemas Identificados en la Arquitectura Original:**
1. **Sobre-ingeniería**: 16 Lambdas para funcionalidades simples
2. **Costos elevados**: $295/mes para solo 1000 usuarios
3. **Complejidad innecesaria**: Múltiples servicios para tareas básicas
4. **Redundancia**: Funciones que podrían consolidarse

#### **📊 Desglose de Costos Actuales (1000 usuarios):**
- **Lambda**: ~$60 (16 funciones) ❌ **OPTIMIZABLE**
- **DynamoDB**: ~$100 ❌ **OPTIMIZABLE**
- **API Gateway**: ~$35 ❌ **OPTIMIZABLE**
- **Cognito**: ~$20 ✅ **NECESARIO**
- **S3 Storage**: ~$25 ✅ **NECESARIO**
- **S3 Requests**: ~$15 ❌ **OPTIMIZABLE**
- **CloudFront**: ~$20 ❌ **OPTIMIZABLE**
- **CloudWatch**: ~$15 ❌ **OPTIMIZABLE**
- **SNS**: ~$5 ❌ **OPTIMIZABLE**
- **Total**: ~$295/mes ❌ **DEMASIADO ALTO**

---

## 🚀 ARQUITECTURA OPTIMIZADA - REDUCCIÓN 70% DE COSTOS

### **🎯 Objetivo: $85/mes para 1000 usuarios (70% menos)**

### **📦 Servicios Optimizados:**

#### **1. 🔄 Consolidación de Lambdas: 16 → 4**
**❌ Antes**: 16 Lambdas especializadas
**✅ Ahora**: 4 Lambdas consolidadas

1. **`hopie-api-handler`** - Todas las APIs REST
2. **`hopie-websocket-handler`** - Chat en tiempo real
3. **`hopie-scheduler`** - Tareas programadas (preguntas, rachas)
4. **`hopie-image-processor`** - Procesamiento de imágenes

**💰 Ahorro**: $60 → $20 = **$40/mes**

#### **2. 🗄️ Base de Datos: DynamoDB OPTIMIZADO**
**❌ Problema DynamoDB Original**:
- $100/mes para 1000 usuarios (mal configurado)
- On-Demand pricing caro
- Índices GSI innecesarios

**✅ Solución DynamoDB Optimizada**:
- **Provisioned Capacity**: RCU/WCU fijos
- **Single Table Design**: Una sola tabla
- **Auto Scaling**: Solo cuando necesario
- **Total**: $25/mes

**💰 Ahorro**: $100 → $25 = **$75/mes**

#### **3. 🌐 API Gateway OPTIMIZADO**
**❌ Problema API Gateway Original**:
- $35/mes (configuración ineficiente)
- Múltiples stages innecesarios
- Caching caro activado

**✅ API Gateway Optimizado**:
- **Single stage**: Solo "prod"
- **Sin caching**: Para reducir costos
- **Throttling optimizado**: Límites justos
- **Total**: $18/mes

**💰 Ahorro**: $35 → $18 = **$17/mes**

#### **4. 📸 Almacenamiento de Imágenes Optimizado**
**❌ Antes**: S3 + CloudFront = $45/mes
**✅ Ahora**:
- **S3 Standard-IA**: $15/mes (acceso infrecuente)
- **CloudFlare CDN**: $0/mes (plan gratuito)

**💰 Ahorro**: $45 → $15 = **$30/mes**

#### **5. 🔔 Notificaciones: SNS → Firebase FCM**
**❌ SNS**: $5/mes
**✅ Firebase FCM**: $0/mes (gratuito hasta 1M mensajes)

**💰 Ahorro**: $5 → $0 = **$5/mes**

#### **6. 📊 Monitoreo: CloudWatch → Grafana Cloud**
**❌ CloudWatch**: $15/mes
**✅ Grafana Cloud**: $0/mes (plan gratuito)

**💰 Ahorro**: $15 → $0 = **$15/mes**

### **📊 COMPARACIÓN DE COSTOS:**

| Servicio | Antes | Después | Ahorro |
|----------|-------|---------|--------|
| Compute (Lambda) | $60 | $20 | $40 |
| Base de Datos | $100 | $15 | $85 |
| API Gateway/ALB | $35 | $18 | $17 |
| Almacenamiento | $45 | $15 | $30 |
| Notificaciones | $5 | $0 | $5 |
| Monitoreo | $15 | $0 | $15 |
| Cognito | $20 | $20 | $0 |
| **TOTAL** | **$295** | **$88** | **$207** |

**🎉 AHORRO TOTAL: 70% ($207/mes)**

---

## 🏗️ NUEVA ARQUITECTURA OPTIMIZADA

### **🔧 Stack Tecnológico Optimizado:**

#### **1. 💻 Compute: 4 Lambdas Consolidadas**
```
┌─ hopie-api-handler ────────────────────────────┐
│ • Autenticación y usuarios                     │
│ • Gestión de parejas                          │
│ • Sistema de árbol y riego                    │
│ • Preguntas diarias                           │
│ • Plan de vida CRUD                           │
│ • Lugares especiales                          │
│ • Estadísticas                                │
│ • Ubicación                                   │
└────────────────────────────────────────────────┘

┌─ hopie-websocket-handler ──────────────────────┐
│ • Chat en tiempo real                         │
│ • Notificaciones en vivo                      │
│ • Estados de conexión                         │
└────────────────────────────────────────────────┘

┌─ hopie-scheduler ──────────────────────────────┐
│ • Preguntas diarias (cron)                    │
│ • Cálculo de rachas (cron)                    │
│ • Limpieza de datos antiguos                  │
└────────────────────────────────────────────────┘

┌─ hopie-image-processor ────────────────────────┐
│ • Redimensionado automático                   │
│ • Generación de thumbnails                    │
│ • Optimización de imágenes                    │
└────────────────────────────────────────────────┘
```

#### **2. 🗄️ Base de Datos: DynamoDB OPTIMIZADO**
**¿Por qué mantener DynamoDB optimizado?**

**✅ Ventajas DynamoDB Optimizado:**
- **NoSQL nativo**: Perfecto para aplicaciones desacopladas
- **Escalabilidad horizontal**: Infinita y automática
- **Performance predecible**: Latencia < 10ms garantizada
- **Serverless**: Cero administración de base de datos
- **Multi-región**: Replicación global automática
- **Streams**: Triggers en tiempo real para desacoplamiento

**✅ Optimizaciones aplicadas:**
- **Single Table Design**: Una tabla para todo
- **Provisioned Capacity**: RCU/WCU fijos más baratos
- **Índices mínimos**: Solo GSI necesarios
- **Particionado inteligente**: Hot partitions evitadas

---

## 🔍 ANÁLISIS DETALLADO DE OPTIMIZACIONES

### **⚡ 1. CONSOLIDACIÓN DE LAMBDAS: 16 → 4**

#### **❌ Arquitectura Original (16 Lambdas):**
```
1. auth-user-management
2. couple-management
3. tree-management
4. tree-level-processor
5. daily-questions
6. question-scheduler
7. life-plan-management
8. places-management
9. chat-websocket
10. chat-message-processor
11. location-management
12. statistics-processor
13. streak-calculator
14. notification-service
15. image-management
16. image-processor
```

#### **✅ Arquitectura Optimizada (4 Lambdas):**

##### **Lambda 1: `hopie-api-handler` (Consolidación de 8 funciones)**
```javascript
// Una sola Lambda maneja múltiples rutas
exports.handler = async (event) => {
  const { httpMethod, path } = event;

  // Router interno
  switch (true) {
    case path.startsWith('/auth'):
      return await authHandler(event);
    case path.startsWith('/couple'):
      return await coupleHandler(event);
    case path.startsWith('/tree'):
      return await treeHandler(event);
    case path.startsWith('/questions'):
      return await questionsHandler(event);
    case path.startsWith('/lifeplan'):
      return await lifePlanHandler(event);
    case path.startsWith('/places'):
      return await placesHandler(event);
    case path.startsWith('/location'):
      return await locationHandler(event);
    case path.startsWith('/stats'):
      return await statsHandler(event);
    default:
      return { statusCode: 404, body: 'Not Found' };
  }
};
```

**Funciones consolidadas:**
- ✅ `auth-user-management` → `/auth/*`
- ✅ `couple-management` → `/couple/*`
- ✅ `tree-management` → `/tree/*`
- ✅ `daily-questions` → `/questions/*`
- ✅ `life-plan-management` → `/lifeplan/*`
- ✅ `places-management` → `/places/*`
- ✅ `location-management` → `/location/*`
- ✅ `statistics-processor` → `/stats/*`

##### **Lambda 2: `hopie-websocket-handler` (Consolidación de 2 funciones)**
```javascript
// WebSocket + procesamiento de mensajes
exports.handler = async (event) => {
  const { eventType, routeKey } = event.requestContext;

  switch (routeKey) {
    case '$connect':
      return await handleConnect(event);
    case '$disconnect':
      return await handleDisconnect(event);
    case 'sendMessage':
      return await handleSendMessage(event);
    case 'getHistory':
      return await handleGetHistory(event);
    default:
      return { statusCode: 400 };
  }
};
```

**Funciones consolidadas:**
- ✅ `chat-websocket` → Conexiones WebSocket
- ✅ `chat-message-processor` → Procesamiento de mensajes

##### **Lambda 3: `hopie-scheduler` (Consolidación de 3 funciones)**
```javascript
// Tareas programadas (cron jobs)
exports.handler = async (event) => {
  const { source, 'detail-type': detailType } = event;

  if (source === 'aws.events') {
    switch (detailType) {
      case 'daily-question-scheduler':
        return await createDailyQuestion();
      case 'streak-calculator':
        return await calculateStreaks();
      case 'cleanup-old-data':
        return await cleanupOldData();
    }
  }
};
```

**Funciones consolidadas:**
- ✅ `question-scheduler` → Crear preguntas diarias
- ✅ `streak-calculator` → Calcular rachas
- ✅ `notification-service` → Notificaciones programadas

##### **Lambda 4: `hopie-image-processor` (Consolidación de 3 funciones)**
```javascript
// Procesamiento de imágenes (S3 triggers)
exports.handler = async (event) => {
  const { eventName, s3 } = event.Records[0];

  if (eventName.startsWith('ObjectCreated')) {
    const bucket = s3.bucket.name;
    const key = s3.object.key;

    // Determinar tipo de procesamiento por ruta
    if (key.includes('/avatars/')) {
      return await processAvatar(bucket, key);
    } else if (key.includes('/places/')) {
      return await processPlaceImage(bucket, key);
    } else if (key.includes('/chat/')) {
      return await processChatImage(bucket, key);
    }
  }
};
```

**Funciones consolidadas:**
- ✅ `image-management` → URLs firmadas y gestión
- ✅ `image-processor` → Procesamiento automático
- ✅ `tree-level-processor` → Lógica de niveles del árbol

#### **💰 Beneficios de la Consolidación:**

| Métrica | Antes (16) | Después (4) | Ahorro |
|---------|------------|-------------|--------|
| **Cold Starts** | 16 funciones | 4 funciones | **75% menos** |
| **Memoria total** | 16 × 512MB | 4 × 1024MB | **50% menos** |
| **Tiempo de deploy** | 16 deploys | 4 deploys | **75% menos** |
| **Complejidad** | Alta | Media | **Simplificado** |
| **Costo Lambda** | $60/mes | $20/mes | **$40/mes** |

### **🌐 2. ALB vs API GATEWAY - ANÁLISIS DETALLADO**

#### **🔍 ¿Son lo mismo? NO, pero el resultado es similar**

##### **API Gateway:**
```
Cliente → API Gateway → Lambda → DynamoDB
         ↓
    - Rate limiting
    - Authentication
    - Request/Response transformation
    - Caching
    - Monitoring
```

##### **Application Load Balancer (ALB):**
```
Cliente → ALB → Lambda → DynamoDB
         ↓
    - Load balancing
    - Health checks
    - SSL termination
    - Path-based routing
```

#### **📊 Comparación Técnica:**

| Característica | API Gateway | ALB | Ganador |
|----------------|-------------|-----|---------|
| **Costo** | $3.50/millón requests | $0.0225/hora + $0.008/LCU | **ALB** |
| **Latencia** | ~100ms | ~50ms | **ALB** |
| **Features** | Muchas (auth, cache, etc.) | Básicas | **API Gateway** |
| **Simplicidad** | Complejo | Simple | **ALB** |
| **Escalabilidad** | Automática | Automática | **Empate** |
| **WebSocket** | Sí (separado) | No nativo | **API Gateway** |

#### **💰 Cálculo de Costos (1000 usuarios, 1M requests/mes):**

**API Gateway:**
```
1M requests × $3.50 = $35/mes
+ WebSocket: $1.25/millón conexiones = $5/mes
Total: $40/mes
```

**ALB:**
```
ALB: $16.20/mes (730 horas)
LCU: 1M requests = ~100 LCU × $0.008 = $8/mes
Total: $24/mes
```

**💰 Ahorro: $40 → $24 = $16/mes**

#### **🤔 ¿Cuándo usar cada uno?**

**✅ Usar ALB cuando:**
- Aplicación simple con APIs REST
- Costo es prioridad
- No necesitas features avanzadas
- Latencia baja es crítica

**✅ Usar API Gateway cuando:**
- Necesitas autenticación integrada
- Requieres rate limiting avanzado
- Transformaciones de request/response
- Caching es importante
- WebSocket es crítico

**🎯 Para Hopie App: ALB es suficiente**
- APIs REST simples
- Autenticación en Cognito (separada)
- WebSocket en Lambda separada
- Costo optimizado

### **🔄 3. REUTILIZACIÓN DE DATA - ARQUITECTURA DESACOPLADA**

#### **✅ DynamoDB para Máximo Desacoplamiento:**

##### **Single Table Design Optimizado:**
```
PK: ENTITY#{type}#{id}
SK: METADATA | RELATION#{type}#{id} | TIMESTAMP#{date}

Ejemplos:
PK: USER#123, SK: METADATA
PK: COUPLE#456, SK: METADATA
PK: COUPLE#456, SK: TREE#PROGRESS
PK: COUPLE#456, SK: MESSAGE#2024-01-15#001
```

##### **DynamoDB Streams para Desacoplamiento:**
```
DynamoDB Change → Stream → Lambda → Otras Apps

Ejemplo:
1. Usuario riega árbol → DynamoDB
2. Stream detecta cambio → Trigger
3. Lambda procesa → Notifica otras apps
4. Apps externas reciben evento
```

#### **🔌 APIs Desacopladas para Reutilización:**

##### **Microservicios por Dominio:**
```javascript
// API para otras aplicaciones
GET /api/v1/couples/{id}/tree-progress
GET /api/v1/couples/{id}/questions-history
GET /api/v1/users/{id}/statistics
POST /api/v1/couples/{id}/events
```

##### **Event-Driven Architecture:**
```javascript
// Eventos que otras apps pueden consumir
{
  "eventType": "TREE_WATERED",
  "coupleId": "456",
  "userId": "123",
  "newLevel": 15,
  "timestamp": "2024-01-15T10:30:00Z"
}

{
  "eventType": "QUESTION_ANSWERED",
  "coupleId": "456",
  "questionId": "daily-2024-01-15",
  "bothAnswered": true
}
```

#### **🚀 Casos de Reutilización:**

##### **App 2: "Family Tree" (Familias)**
```javascript
// Reutiliza la misma data structure
PK: FAMILY#789, SK: METADATA
PK: FAMILY#789, SK: TREE#PROGRESS
PK: FAMILY#789, SK: MEMBER#123
```

##### **App 3: "Friend Groups" (Grupos de Amigos)**
```javascript
// Misma arquitectura, diferente contexto
PK: GROUP#101, SK: METADATA
PK: GROUP#101, SK: ACTIVITY#CHALLENGE
PK: GROUP#101, SK: MESSAGE#2024-01-15#001
```

##### **App 4: "Corporate Teams" (Equipos de Trabajo)**
```javascript
// Reutilización total de la lógica
PK: TEAM#202, SK: METADATA
PK: TEAM#202, SK: PROJECT#PROGRESS
PK: TEAM#202, SK: GOAL#001
```

#### **🔧 Ventajas del Desacoplamiento:**

1. **Data Portability**: Misma estructura para múltiples apps
2. **API Reusability**: Endpoints genéricos reutilizables
3. **Event Streaming**: Otras apps se suscriben a eventos
4. **Horizontal Scaling**: Cada app escala independiente
5. **Technology Agnostic**: Frontend puede ser React, Flutter, etc.

### **📊 COSTOS FINALES OPTIMIZADOS:**

| Servicio | Original | Optimizado | Ahorro |
|----------|----------|------------|--------|
| **Lambda** | $60 | $20 | $40 |
| **DynamoDB** | $100 | $25 | $75 |
| **API Layer** | $35 | $18 | $17 |
| **CDN** | $20 | $0 | $20 |
| **Storage** | $25 | $15 | $10 |
| **Notificaciones** | $5 | $0 | $5 |
| **Monitoreo** | $15 | $0 | $15 |
| **Cognito** | $20 | $20 | $0 |
| **TOTAL** | **$295** | **$98** | **$197** |

**🎉 AHORRO: 67% ($197/mes) manteniendo NoSQL y máximo desacoplamiento**

---

## 🎯 ARQUITECTURA FINAL OPTIMIZADA - DYNAMODB + API GATEWAY

### **🔧 JUSTIFICACIÓN TÉCNICA:**

#### **✅ Por qué mantener DynamoDB:**
1. **Patrones de acceso ya definidos**: Single table design optimizado
2. **Escalabilidad horizontal**: Infinita y automática
3. **Performance predecible**: < 10ms latencia garantizada
4. **Desacoplamiento máximo**: DynamoDB Streams para eventos
5. **Reutilización**: Misma estructura para múltiples apps

#### **✅ Por qué mantener API Gateway:**
1. **Integración nativa con Cognito**: Authorizers automáticos
2. **Versionado built-in**: `/v1`, `/v2` sin configuración extra
3. **Rate limiting**: Por usuario/API key automático
4. **CORS**: Configuración simple para web apps
5. **Request/Response transformation**: Para compatibilidad

### **💰 OPTIMIZACIONES APLICADAS:**

#### **🗄️ DynamoDB Optimizado:**
```json
{
  "TableName": "hopie-main-table",
  "BillingMode": "PROVISIONED",
  "ProvisionedThroughput": {
    "ReadCapacityUnits": 5,
    "WriteCapacityUnits": 5
  },
  "GlobalSecondaryIndexes": [
    {
      "IndexName": "GSI1",
      "Keys": {
        "PartitionKey": "GSI1PK",
        "SortKey": "GSI1SK"
      },
      "ProvisionedThroughput": {
        "ReadCapacityUnits": 2,
        "WriteCapacityUnits": 2
      }
    }
  ],
  "StreamSpecification": {
    "StreamEnabled": true,
    "StreamViewType": "NEW_AND_OLD_IMAGES"
  }
}
```

**💰 Costo DynamoDB:**
- **Base**: 5 RCU + 5 WCU = $3.25/mes
- **GSI**: 2 RCU + 2 WCU = $1.30/mes
- **Storage**: 1GB = $0.25/mes
- **Streams**: $0.02/100K records = $1/mes
- **Total**: **$5.80/mes** (vs $100/mes original)

#### **🌐 API Gateway Optimizado:**
```yaml
# Configuración optimizada
Resources:
  HopieAPI:
    Type: AWS::ApiGateway::RestApi
    Properties:
      Name: hopie-api
      EndpointConfiguration:
        Types: [REGIONAL]  # Más barato que EDGE

  # Versionado automático
  V1Deployment:
    Type: AWS::ApiGateway::Deployment
    Properties:
      RestApiId: !Ref HopieAPI
      StageName: v1

  V2Deployment:
    Type: AWS::ApiGateway::Deployment
    Properties:
      RestApiId: !Ref HopieAPI
      StageName: v2

  # Throttling optimizado
  UsagePlan:
    Type: AWS::ApiGateway::UsagePlan
    Properties:
      Throttle:
        BurstLimit: 100    # Suficiente para picos
        RateLimit: 50      # 50 req/sec promedio
      Quota:
        Limit: 100000      # 100K requests/mes por usuario
        Period: MONTH
```

**💰 Costo API Gateway:**
- **Requests**: 1M/mes × $3.50 = $3.50/mes
- **Regional endpoint**: Sin costo extra
- **Sin caching**: $0/mes
- **Total**: **$3.50/mes** (vs $35/mes original)

### **📊 PATRONES DE ACCESO OPTIMIZADOS:**

#### **Single Table Design Final:**
```
PK: ENTITY#{type}#{id}
SK: METADATA | RELATION#{type}#{id} | TIMESTAMP#{date}

GSI1PK: TYPE#{entityType}
GSI1SK: CREATED#{timestamp} | STATUS#{status}

Ejemplos optimizados:
┌─────────────────────────────────────────────────────────────┐
│ PK: USER#123           SK: METADATA                        │
│ PK: COUPLE#456         SK: METADATA                        │
│ PK: COUPLE#456         SK: TREE#PROGRESS                   │
│ PK: COUPLE#456         SK: MESSAGE#2024-01-15#001          │
│ PK: COUPLE#456         SK: QUESTION#2024-01-15#123         │
│ PK: COUPLE#456         SK: PLACE#789                       │
│ PK: COUPLE#456         SK: LIFEPLAN#001                    │
│                                                             │
│ GSI1PK: TYPE#COUPLE    GSI1SK: CREATED#2024-01-01         │
│ GSI1PK: TYPE#MESSAGE   GSI1SK: TIMESTAMP#1705334400000    │
└─────────────────────────────────────────────────────────────┘
```

#### **Consultas Eficientes:**
```javascript
// 1. Obtener progreso del árbol
const treeProgress = await dynamodb.get({
  TableName: 'hopie-main-table',
  Key: {
    PK: 'COUPLE#456',
    SK: 'TREE#PROGRESS'
  }
}).promise();

// 2. Obtener mensajes del día
const todayMessages = await dynamodb.query({
  TableName: 'hopie-main-table',
  KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
  ExpressionAttributeValues: {
    ':pk': 'COUPLE#456',
    ':sk': 'MESSAGE#2024-01-15'
  }
}).promise();

// 3. Obtener todas las parejas (para admin)
const allCouples = await dynamodb.query({
  TableName: 'hopie-main-table',
  IndexName: 'GSI1',
  KeyConditionExpression: 'GSI1PK = :type',
  ExpressionAttributeValues: {
    ':type': 'TYPE#COUPLE'
  }
}).promise();
```

### **🔄 VERSIONADO Y REUTILIZACIÓN:**

#### **API Versionado:**
```
# V1 - Hopie App (Parejas)
GET /v1/couples/{id}/tree-progress
POST /v1/couples/{id}/water-tree
GET /v1/couples/{id}/questions/today

# V2 - Hopie App (Mejorado)
GET /v2/couples/{id}/tree-progress  # Con más datos
POST /v2/couples/{id}/activities    # Actividades generalizadas
GET /v2/couples/{id}/insights       # Analytics avanzado

# V1 - Family App (Reutilización)
GET /v1/families/{id}/progress
POST /v1/families/{id}/activities
GET /v1/families/{id}/challenges

# V1 - Corporate App (Reutilización)
GET /v1/teams/{id}/progress
POST /v1/teams/{id}/goals
GET /v1/teams/{id}/metrics
```

#### **Estructura Reutilizable:**
```javascript
// Configuración por tipo de entidad
const entityConfigs = {
  COUPLE: {
    progressMetrics: ['treeLevel', 'experiencePoints'],
    activities: ['waterTree', 'answerQuestion'],
    relationships: ['user1', 'user2']
  },
  FAMILY: {
    progressMetrics: ['familyLevel', 'bonusPoints'],
    activities: ['completeChallenge', 'shareMemory'],
    relationships: ['parents', 'children']
  },
  TEAM: {
    progressMetrics: ['teamLevel', 'productivityScore'],
    activities: ['completeGoal', 'collaborate'],
    relationships: ['members', 'leader']
  }
};

// Función genérica reutilizable
async function getEntityProgress(entityType, entityId) {
  const config = entityConfigs[entityType];
  return await dynamodb.get({
    TableName: 'hopie-main-table',
    Key: {
      PK: `${entityType}#${entityId}`,
      SK: 'METADATA'
    }
  }).promise();
}
```

### **💰 COSTOS FINALES OPTIMIZADOS:**

| Servicio | Original | Optimizado | Ahorro |
|----------|----------|------------|--------|
| **Lambda** | $60 | $20 | $40 |
| **DynamoDB** | $100 | $6 | $94 |
| **API Gateway** | $35 | $4 | $31 |
| **S3 + CDN** | $45 | $15 | $30 |
| **Cognito** | $20 | $20 | $0 |
| **Otros** | $35 | $5 | $30 |
| **TOTAL** | **$295** | **$70** | **$225** |

**🎉 AHORRO FINAL: 76% ($225/mes)**

---

## 🗺️ INTEGRACIÓN GOOGLE MAPS - ESPECIFICACIÓN COMPLETA

### **📍 ARQUITECTURA DE GEOLOCALIZACIÓN**

#### **🔄 Flujo de Geolocalización:**
```
Frontend App → Google Maps API → Mostrar ubicaciones
     ↓
Backend AWS ← Consulta lugares guardados ← DynamoDB
     ↓
Frontend ← Coordenadas (lat/lng) ← Lambda Function
     ↓
Google Maps ← Renderizar marcadores ← Frontend
```

#### **🌐 Servicios Involucrados:**
1. **Google Maps JavaScript API** - Renderizado de mapas
2. **Google Maps Geocoding API** - Conversión dirección ↔ coordenadas
3. **Google Maps Distance Matrix API** - Cálculo de distancias
4. **AWS Lambda** - Consulta de lugares guardados
5. **DynamoDB** - Almacenamiento de coordenadas

### **📱 FUNCIONALIDADES DE GEOLOCALIZACIÓN:**

#### **1. 📍 Ubicación en Tiempo Real (Solo Frontend)**
```javascript
// Obtener ubicación actual (sin AWS)
navigator.geolocation.getCurrentPosition(
  (position) => {
    const userLocation = {
      lat: position.coords.latitude,
      lng: position.coords.longitude,
      accuracy: position.coords.accuracy,
      timestamp: Date.now()
    };

    // Mostrar en Google Maps
    displayUserLocation(userLocation);

    // Calcular distancia entre parejas (frontend)
    calculateDistance(userLocation, partnerLocation);
  },
  (error) => console.error('Error getting location:', error),
  {
    enableHighAccuracy: true,
    timeout: 10000,
    maximumAge: 300000 // 5 minutos
  }
);
```

#### **2. 🏠 Lugares Especiales (AWS + Google Maps)**
```javascript
// Botón "Mostrar nuestros lugares"
async function showOurPlaces() {
  try {
    // 1. Consultar lugares guardados en AWS
    const response = await fetch('/api/v1/places', {
      headers: {
        'Authorization': `Bearer ${cognitoToken}`,
        'Content-Type': 'application/json'
      }
    });

    const places = await response.json();

    // 2. Renderizar marcadores en Google Maps
    places.forEach(place => {
      const marker = new google.maps.Marker({
        position: { lat: place.latitude, lng: place.longitude },
        map: googleMap,
        title: place.name,
        icon: getPlaceIcon(place.category),
        animation: google.maps.Animation.DROP
      });

      // 3. Info window con detalles
      const infoWindow = new google.maps.InfoWindow({
        content: createPlaceInfoContent(place)
      });

      marker.addListener('click', () => {
        infoWindow.open(googleMap, marker);
      });
    });

  } catch (error) {
    console.error('Error loading places:', error);
  }
}
```

#### **3. 📏 Cálculo de Distancias (Frontend)**
```javascript
// Calcular distancia entre dos puntos
function calculateDistance(location1, location2) {
  const service = new google.maps.DistanceMatrixService();

  service.getDistanceMatrix({
    origins: [{ lat: location1.lat, lng: location1.lng }],
    destinations: [{ lat: location2.lat, lng: location2.lng }],
    travelMode: google.maps.TravelMode.DRIVING,
    unitSystem: google.maps.UnitSystem.METRIC,
    avoidHighways: false,
    avoidTolls: false
  }, (response, status) => {
    if (status === 'OK') {
      const distance = response.rows[0].elements[0].distance.text;
      const duration = response.rows[0].elements[0].duration.text;

      displayDistanceInfo(distance, duration);
    }
  });
}
```

### **🔧 CONFIGURACIÓN GOOGLE MAPS API**

#### **📋 APIs Requeridas:**
```yaml
Google Cloud Console Configuration:
  Project: hopie-app-maps
  APIs_Enabled:
    - Maps JavaScript API
    - Geocoding API
    - Distance Matrix API
    - Places API (opcional para autocompletado)

  API_Key_Restrictions:
    HTTP_Referrers:
      - https://hopie-app.com/*
      - https://*.hopie-app.com/*
      - http://localhost:3000/* # Para desarrollo

    API_Restrictions:
      - Maps JavaScript API
      - Geocoding API
      - Distance Matrix API
```

#### **💰 Costos Google Maps (1000 usuarios activos):**
```
Maps JavaScript API: $7/1000 cargas = $7/mes
Geocoding API: $5/1000 requests = $2/mes
Distance Matrix API: $5/1000 requests = $3/mes
Total Google Maps: $12/mes
```

### **🏗️ BACKEND AWS - LUGARES ESPECIALES**

#### **📊 Estructura DynamoDB para Lugares:**
```json
{
  "PK": "COUPLE#456",
  "SK": "PLACE#789",
  "placeId": "789",
  "name": "Nuestro primer café",
  "description": "Donde tuvimos nuestra primera cita",
  "category": "cafe",
  "latitude": 19.4326,
  "longitude": -99.1332,
  "address": "Calle Principal 123, Ciudad",
  "mainImageUrl": "couples/456/places/789/main.jpg",
  "thumbnailUrl": "couples/456/places/789/thumb.jpg",
  "galleryUrls": [
    "couples/456/places/789/gallery/img1.jpg",
    "couples/456/places/789/gallery/img2.jpg"
  ],
  "visitedAt": "2024-01-01T00:00:00Z",
  "createdAt": "2024-01-02T00:00:00Z",
  "isFavorite": true,
  "notes": "Aquí nos conocimos mejor",
  "rating": 5,
  "GSI1PK": "TYPE#PLACE",
  "GSI1SK": "COUPLE#456#CREATED#2024-01-02"
}
```

#### **⚡ Lambda Function: Places Management**
```javascript
// hopie-api-handler - Gestión de lugares
const placesHandler = async (event) => {
  const { httpMethod, pathParameters, body } = event;
  const coupleId = await getCoupleIdFromToken(event);

  switch (httpMethod) {
    case 'GET':
      return await getPlaces(coupleId);
    case 'POST':
      return await createPlace(coupleId, JSON.parse(body));
    case 'PUT':
      return await updatePlace(coupleId, pathParameters.placeId, JSON.parse(body));
    case 'DELETE':
      return await deletePlace(coupleId, pathParameters.placeId);
    default:
      return { statusCode: 405, body: 'Method Not Allowed' };
  }
};

// Obtener todos los lugares de una pareja
async function getPlaces(coupleId) {
  const params = {
    TableName: 'hopie-main-table',
    KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
    ExpressionAttributeValues: {
      ':pk': `COUPLE#${coupleId}`,
      ':sk': 'PLACE#'
    }
  };

  const result = await dynamodb.query(params).promise();

  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    },
    body: JSON.stringify({
      places: result.Items.map(item => ({
        id: item.placeId,
        name: item.name,
        description: item.description,
        category: item.category,
        latitude: item.latitude,
        longitude: item.longitude,
        address: item.address,
        mainImageUrl: item.mainImageUrl ? `${CDN_URL}/${item.mainImageUrl}` : null,
        thumbnailUrl: item.thumbnailUrl ? `${CDN_URL}/${item.thumbnailUrl}` : null,
        isFavorite: item.isFavorite || false,
        visitedAt: item.visitedAt,
        createdAt: item.createdAt,
        notes: item.notes,
        rating: item.rating
      }))
    })
  };
}

// Crear nuevo lugar
async function createPlace(coupleId, placeData) {
  const placeId = generateUUID();
  const now = new Date().toISOString();

  // Validar coordenadas
  if (!isValidCoordinate(placeData.latitude, placeData.longitude)) {
    return {
      statusCode: 400,
      body: JSON.stringify({ error: 'Invalid coordinates' })
    };
  }

  const place = {
    PK: `COUPLE#${coupleId}`,
    SK: `PLACE#${placeId}`,
    placeId,
    name: placeData.name,
    description: placeData.description || '',
    category: placeData.category || 'other',
    latitude: parseFloat(placeData.latitude),
    longitude: parseFloat(placeData.longitude),
    address: placeData.address || '',
    createdAt: now,
    isFavorite: placeData.isFavorite || false,
    notes: placeData.notes || '',
    rating: placeData.rating || 0,
    GSI1PK: 'TYPE#PLACE',
    GSI1SK: `COUPLE#${coupleId}#CREATED#${now}`
  };

  await dynamodb.put({
    TableName: 'hopie-main-table',
    Item: place
  }).promise();

  return {
    statusCode: 201,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    },
    body: JSON.stringify({
      message: 'Place created successfully',
      placeId
    })
  };
}

// Validar coordenadas
function isValidCoordinate(lat, lng) {
  return (
    typeof lat === 'number' &&
    typeof lng === 'number' &&
    lat >= -90 && lat <= 90 &&
    lng >= -180 && lng <= 180
  );
}
```

### **🔗 API ENDPOINTS - LUGARES ESPECIALES**

#### **📋 Endpoints Completos:**
```yaml
Places Management:
  GET /api/v1/places:
    description: Obtener todos los lugares de la pareja
    auth: Cognito JWT
    response: Array de lugares con coordenadas

  POST /api/v1/places:
    description: Crear nuevo lugar especial
    auth: Cognito JWT
    body:
      name: string (required)
      description: string
      category: string (cafe|restaurant|park|home|other)
      latitude: number (required)
      longitude: number (required)
      address: string
      isFavorite: boolean
      notes: string
      rating: number (1-5)

  GET /api/v1/places/{placeId}:
    description: Obtener lugar específico
    auth: Cognito JWT

  PUT /api/v1/places/{placeId}:
    description: Actualizar lugar existente
    auth: Cognito JWT
    body: Mismos campos que POST

  DELETE /api/v1/places/{placeId}:
    description: Eliminar lugar
    auth: Cognito JWT

  POST /api/v1/places/{placeId}/visit:
    description: Marcar lugar como visitado
    auth: Cognito JWT
    body:
      visitedAt: ISO timestamp
      notes: string
```

### **🎨 FRONTEND - INTEGRACIÓN GOOGLE MAPS**

#### **📱 Componente React/Flutter:**
```javascript
// React Component para Google Maps
import { GoogleMap, Marker, InfoWindow } from '@react-google-maps/api';

const HopieMap = ({ coupleId }) => {
  const [places, setPlaces] = useState([]);
  const [userLocation, setUserLocation] = useState(null);
  const [partnerLocation, setPartnerLocation] = useState(null);
  const [showPlaces, setShowPlaces] = useState(false);
  const [selectedPlace, setSelectedPlace] = useState(null);

  // Cargar lugares desde AWS
  const loadPlaces = async () => {
    try {
      const response = await fetch('/api/v1/places', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const data = await response.json();
      setPlaces(data.places);
      setShowPlaces(true);
    } catch (error) {
      console.error('Error loading places:', error);
    }
  };

  // Obtener ubicación actual
  useEffect(() => {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        setUserLocation({
          lat: position.coords.latitude,
          lng: position.coords.longitude
        });
      }
    );
  }, []);

  return (
    <div className="map-container">
      <div className="map-controls">
        <button onClick={loadPlaces} className="show-places-btn">
          {showPlaces ? 'Ocultar lugares' : 'Mostrar nuestros lugares'}
        </button>
      </div>

      <GoogleMap
        mapContainerStyle={{ width: '100%', height: '400px' }}
        center={userLocation || { lat: 19.4326, lng: -99.1332 }}
        zoom={13}
      >
        {/* Marcador usuario actual */}
        {userLocation && (
          <Marker
            position={userLocation}
            icon={{
              url: '/icons/user-location.png',
              scaledSize: new window.google.maps.Size(40, 40)
            }}
            title="Tu ubicación"
          />
        )}

        {/* Marcador pareja */}
        {partnerLocation && (
          <Marker
            position={partnerLocation}
            icon={{
              url: '/icons/partner-location.png',
              scaledSize: new window.google.maps.Size(40, 40)
            }}
            title="Tu pareja"
          />
        )}

        {/* Marcadores de lugares especiales */}
        {showPlaces && places.map(place => (
          <Marker
            key={place.id}
            position={{ lat: place.latitude, lng: place.longitude }}
            icon={{
              url: `/icons/places/${place.category}.png`,
              scaledSize: new window.google.maps.Size(30, 30)
            }}
            title={place.name}
            onClick={() => setSelectedPlace(place)}
          />
        ))}

        {/* Info Window para lugar seleccionado */}
        {selectedPlace && (
          <InfoWindow
            position={{
              lat: selectedPlace.latitude,
              lng: selectedPlace.longitude
            }}
            onCloseClick={() => setSelectedPlace(null)}
          >
            <div className="place-info">
              <h3>{selectedPlace.name}</h3>
              <p>{selectedPlace.description}</p>
              {selectedPlace.thumbnailUrl && (
                <img
                  src={selectedPlace.thumbnailUrl}
                  alt={selectedPlace.name}
                  style={{ width: '100px', height: '75px' }}
                />
              )}
              <div className="place-actions">
                <button onClick={() => navigateToPlace(selectedPlace)}>
                  Ir al lugar
                </button>
              </div>
            </div>
          </InfoWindow>
        )}
      </GoogleMap>
    </div>
  );
};
```

### **📋 VARIABLES DE ENTORNO REQUERIDAS**

#### **🔐 Configuración Completa:**
```yaml
# AWS Lambda Environment Variables
Environment:
  Variables:
    # DynamoDB
    DYNAMODB_TABLE_NAME: hopie-main-table
    DYNAMODB_REGION: us-east-1

    # S3 y CDN
    S3_BUCKET_USER_CONTENT: hopie-user-content-prod
    S3_BUCKET_APP_ASSETS: hopie-app-assets-prod
    CDN_URL: https://d123abc.cloudfront.net

    # Google Maps (solo para geocoding desde backend si es necesario)
    GOOGLE_MAPS_API_KEY: !Ref GoogleMapsApiKey

    # Cognito
    COGNITO_USER_POOL_ID: !Ref CognitoUserPool
    COGNITO_CLIENT_ID: !Ref CognitoUserPoolClient

    # Otros
    NODE_ENV: production
    LOG_LEVEL: info

# Frontend Environment Variables (.env)
REACT_APP_API_BASE_URL=https://api.hopie-app.com/v1
REACT_APP_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
REACT_APP_COGNITO_USER_POOL_ID=us-east-1_xxxxxxxxx
REACT_APP_COGNITO_CLIENT_ID=xxxxxxxxxxxxxxxxxxxxxxxxxx
REACT_APP_CDN_URL=https://d123abc.cloudfront.net
```

**📊 Esquema de Base de Datos Optimizado:**
```sql
-- Usuarios
CREATE TABLE users (
    id UUID PRIMARY KEY,
    email VARCHAR(255) UNIQUE,
    name VARCHAR(255),
    avatar_url TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Parejas
CREATE TABLE couples (
    id UUID PRIMARY KEY,
    user1_id UUID REFERENCES users(id),
    user2_id UUID REFERENCES users(id),
    tree_type VARCHAR(50),
    level INTEGER DEFAULT 1,
    experience_points INTEGER DEFAULT 0,
    current_stage VARCHAR(50) DEFAULT 'semilla',
    created_at TIMESTAMP DEFAULT NOW()
);

-- Riego diario (particionado por fecha)
CREATE TABLE waterings (
    id UUID PRIMARY KEY,
    couple_id UUID REFERENCES couples(id),
    user_id UUID REFERENCES users(id),
    watered_date DATE,
    experience_gained INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
) PARTITION BY RANGE (watered_date);

-- Preguntas diarias
CREATE TABLE daily_questions (
    id UUID PRIMARY KEY,
    question_date DATE UNIQUE,
    question_text TEXT,
    category VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Respuestas
CREATE TABLE question_answers (
    id UUID PRIMARY KEY,
    couple_id UUID REFERENCES couples(id),
    user_id UUID REFERENCES users(id),
    question_id UUID REFERENCES daily_questions(id),
    answer_text TEXT,
    answered_at TIMESTAMP DEFAULT NOW()
);

-- Plan de vida
CREATE TABLE life_plan_stages (
    id UUID PRIMARY KEY,
    couple_id UUID REFERENCES couples(id),
    title VARCHAR(255),
    description TEXT,
    status VARCHAR(20) DEFAULT 'pending',
    target_date DATE,
    completed_at TIMESTAMP,
    display_order INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Lugares especiales
CREATE TABLE special_places (
    id UUID PRIMARY KEY,
    couple_id UUID REFERENCES couples(id),
    name VARCHAR(255),
    description TEXT,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    category VARCHAR(50),
    main_image_url TEXT,
    thumbnail_url TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Chat (particionado por fecha)
CREATE TABLE chat_messages (
    id UUID PRIMARY KEY,
    couple_id UUID REFERENCES couples(id),
    sender_id UUID REFERENCES users(id),
    content TEXT,
    message_type VARCHAR(20) DEFAULT 'text',
    attachment_url TEXT,
    sent_at TIMESTAMP DEFAULT NOW()
) PARTITION BY RANGE (sent_at);

-- Índices optimizados
CREATE INDEX idx_couples_users ON couples(user1_id, user2_id);
CREATE INDEX idx_waterings_couple_date ON waterings(couple_id, watered_date);
CREATE INDEX idx_messages_couple_time ON chat_messages(couple_id, sent_at DESC);
CREATE INDEX idx_answers_couple_question ON question_answers(couple_id, question_id);
```

#### **3. 🌐 Load Balancer + CloudFlare**
```
Internet → CloudFlare CDN → ALB → Lambda Functions
                ↓
            Cache estático (imágenes, assets)
```

**Beneficios:**
- **CloudFlare gratuito**: CDN global sin costo
- **ALB**: Más barato que API Gateway para alto volumen
- **SSL automático**: Certificados gratuitos
- **DDoS protection**: Incluido en CloudFlare

#### **4. 📱 Notificaciones: Firebase FCM**
**¿Por qué Firebase en lugar de SNS?**

**✅ Firebase FCM:**
- **Gratuito**: Hasta 1M mensajes/mes
- **Multiplataforma**: iOS, Android, Web
- **Rich notifications**: Imágenes, botones, acciones
- **Analytics incluido**: Métricas de entrega

**❌ SNS:**
- **Costo**: $0.50 por millón de mensajes
- **Configuración compleja**: Múltiples servicios
- **Limitado**: Solo push básico

#### **5. 📊 Monitoreo: Grafana Cloud + Prometheus**
**Stack de monitoreo gratuito:**
- **Grafana Cloud**: 10K métricas gratis
- **Prometheus**: Métricas de aplicación
- **Alertas**: Email/Slack gratuitas
- **Dashboards**: Visualización avanzada

---

## ⚖️ ANÁLISIS: ¿CUÁNDO USAR AWS vs ALTERNATIVAS?

### **🟢 SERVICIOS AWS QUE SÍ VALEN LA PENA:**

#### **1. 🔐 AWS Cognito - MANTENER**
**¿Por qué es indispensable?**
- **Integración nativa**: Con Lambda y RDS
- **Escalabilidad automática**: Sin configuración
- **Seguridad robusta**: JWT, MFA, OAuth
- **Costo razonable**: $20/mes para 1000 usuarios
- **Alternativas complejas**: Auth0 ($23/mes), Firebase Auth (limitado)

**✅ VEREDICTO: MANTENER AWS COGNITO**

#### **2. 💾 AWS S3 - MANTENER (Optimizado)**
**¿Por qué es la mejor opción?**
- **Durabilidad**: 99.999999999% (11 9's)
- **Escalabilidad infinita**: Sin límites
- **Integración**: Con Lambda, CloudFront
- **Costo optimizado**: Standard-IA para imágenes

**Alternativas evaluadas:**
- **Google Cloud Storage**: Similar costo
- **Azure Blob**: Más caro
- **DigitalOcean Spaces**: Menos confiable

**✅ VEREDICTO: MANTENER S3 (con optimizaciones)**

#### **3. ⚡ AWS Lambda - MANTENER (Consolidado)**
**¿Por qué sigue siendo óptimo?**
- **Pay-per-use**: Solo pagas por ejecución
- **Escalabilidad automática**: 0 a 1000 concurrentes
- **Sin servidor**: Cero mantenimiento
- **Integración**: Con todos los servicios AWS

**Alternativas evaluadas:**
- **Google Cloud Functions**: Similar costo
- **Vercel Functions**: Limitado
- **Railway**: Más caro para este volumen

**✅ VEREDICTO: MANTENER LAMBDA (4 funciones consolidadas)**

### **🔴 SERVICIOS AWS QUE NO VALEN LA PENA:**

#### **1. ❌ DynamoDB - REEMPLAZAR**
**¿Por qué no es óptimo para Hopie?**
- **Costo alto**: $100/mes para datos simples
- **Complejidad innecesaria**: Para relaciones simples
- **Consultas limitadas**: Solo PK/SK
- **Over-engineering**: Para 1000 usuarios

**✅ MEJOR ALTERNATIVA: PostgreSQL RDS**
- **Costo**: $15/mes vs $100/mes
- **Flexibilidad**: SQL completo
- **Herramientas**: pgAdmin, backups
- **Escalabilidad**: Vertical simple

#### **2. ❌ API Gateway - REEMPLAZAR**
**¿Por qué es caro?**
- **Costo por request**: $3.50 por millón
- **Features innecesarias**: Para APIs simples
- **Complejidad**: Configuración excesiva

**✅ MEJOR ALTERNATIVA: Application Load Balancer**
- **Costo fijo**: $16/mes + $0.008/LCU
- **Simplicidad**: Configuración directa
- **Performance**: Menor latencia

#### **3. ❌ CloudFront - REEMPLAZAR**
**¿Por qué no es necesario?**
- **Costo**: $20/mes para CDN básico
- **Complejidad**: Configuración AWS

**✅ MEJOR ALTERNATIVA: CloudFlare**
- **Costo**: $0/mes (plan gratuito)
- **Performance**: Red global
- **Simplicidad**: Setup en minutos

#### **4. ❌ SNS - REEMPLAZAR**
**¿Por qué no es óptimo?**
- **Costo**: $0.50 por millón
- **Limitaciones**: Push básico
- **Configuración**: Compleja

**✅ MEJOR ALTERNATIVA: Firebase FCM**
- **Costo**: $0/mes hasta 1M mensajes
- **Features**: Rich notifications
- **Simplicidad**: SDK directo

---

## 🎯 ESTRATEGIA DE MIGRACIÓN

### **📅 Plan de Implementación Optimizado:**

#### **Fase 1: Base (2-3 semanas)**
1. **Setup RDS PostgreSQL** (1 día)
2. **Migrar esquema de DynamoDB a SQL** (3 días)
3. **Consolidar Lambdas** (1 semana)
4. **Setup ALB** (1 día)

#### **Fase 2: Optimizaciones (1-2 semanas)**
1. **Implementar CloudFlare CDN** (2 días)
2. **Migrar a Firebase FCM** (2 días)
3. **Setup Grafana monitoring** (1 día)
4. **Optimizar S3 storage class** (1 día)

#### **Fase 3: Testing (1 semana)**
1. **Load testing** (2 días)
2. **Performance optimization** (2 días)
3. **Cost monitoring setup** (1 día)

### **📊 ROI de la Optimización:**

| Métrica | Antes | Después | Mejora |
|---------|-------|---------|--------|
| **Costo mensual** | $295 | $88 | **70% menos** |
| **Costo por usuario** | $0.295 | $0.088 | **70% menos** |
| **Lambdas a mantener** | 16 | 4 | **75% menos** |
| **Complejidad** | Alta | Media | **Simplificado** |
| **Escalabilidad** | Buena | Excelente | **Mejorada** |

### **🚨 Riesgos y Mitigaciones:**

#### **Riesgo 1: Migración de datos**
- **Mitigación**: Scripts de migración + testing exhaustivo
- **Rollback**: Mantener DynamoDB hasta validación completa

#### **Riesgo 2: Latencia de base de datos**
- **Mitigación**: Connection pooling + índices optimizados
- **Monitoring**: Métricas de performance en tiempo real

#### **Riesgo 3: Vendor lock-in reducido**
- **Beneficio**: Menos dependencia de AWS
- **Portabilidad**: PostgreSQL es estándar

---

## 💡 RECOMENDACIONES FINALES

### **🎯 Para Hopie App específicamente:**

#### **✅ USAR AWS para:**
1. **Cognito** - Autenticación robusta y escalable
2. **Lambda** - Compute serverless (4 funciones consolidadas)
3. **S3** - Almacenamiento de imágenes confiable
4. **RDS PostgreSQL** - Base de datos relacional

#### **❌ NO USAR AWS para:**
1. **DynamoDB** - Demasiado caro para datos relacionales simples
2. **API Gateway** - ALB es más económico
3. **CloudFront** - CloudFlare gratuito es suficiente
4. **SNS** - Firebase FCM es gratuito y mejor

#### **🏆 Arquitectura Final Recomendada:**
```
CloudFlare CDN → ALB → 4 Lambda Functions → RDS PostgreSQL
                                        ↓
                                    S3 (Standard-IA)
                                        ↓
                                Firebase FCM (Push)
                                        ↓
                                Grafana Cloud (Monitoring)
```

### **💰 Resultado Final:**
- **Costo**: $88/mes (70% menos)
- **Mantenimiento**: Simplificado
- **Performance**: Mejorado
- **Escalabilidad**: Mantenida
- **Confiabilidad**: Igual o mejor

**🎉 CONCLUSIÓN: Arquitectura optimizada que mantiene toda la funcionalidad con 70% menos costo**

### **📦 Configuración de S3 y CloudFront**

#### **Bucket Policies y Permisos:**
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "AllowCloudFrontAccess",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity"
      },
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::hopie-app-assets-prod/*"
    },
    {
      "Sid": "AllowLambdaUpload",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::ACCOUNT:role/hopie-lambda-execution-role"
      },
      "Action": [
        "s3:PutObject",
        "s3:DeleteObject",
        "s3:GetObject"
      ],
      "Resource": "arn:aws:s3:::hopie-user-content-prod/*"
    }
  ]
}
```

#### **CloudFront Distributions:**
- **User Content**: `https://d123abc.cloudfront.net/` → `hopie-user-content-prod`
- **App Assets**: `https://d456def.cloudfront.net/` → `hopie-app-assets-prod`

#### **Configuración de Cache:**
- **App Assets**: Cache por 1 año (inmutable)
- **User Content**: Cache por 24 horas
- **Thumbnails**: Cache por 7 días

---

## 🚀 Plan de Implementación

### **Fase 1: Core Backend (4-6 semanas)**
1. Configurar DynamoDB y estructura de datos
2. Implementar autenticación con Cognito
3. Crear APIs de gestión de usuarios y parejas
4. Sistema básico de árbol y riego

### **Fase 2: Funcionalidades Principales (4-6 semanas)**
1. Sistema de preguntas diarias
2. Plan de vida CRUD
3. Lugares especiales
4. Estadísticas básicas

### **Fase 3: Tiempo Real (3-4 semanas)**
1. Chat con WebSocket
2. Ubicación en tiempo real
3. Notificaciones push
4. Optimizaciones de rendimiento

### **Fase 4: Optimización (2-3 semanas)**
1. Monitoreo y logging
2. Pruebas de carga
3. Optimización de costos
4. Documentación final

---

## 📝 Consideraciones Técnicas

### **Seguridad:**
- Autenticación JWT con Cognito
- Autorización a nivel de pareja
- Encriptación de datos sensibles
- Rate limiting en APIs

### **Escalabilidad:**
- DynamoDB con auto-scaling
- Lambda con concurrencia reservada
- CloudFront para contenido estático
- Índices GSI optimizados

### **Monitoreo:**
- CloudWatch Logs y Metrics
- X-Ray para tracing
- Alarmas automáticas
- Dashboard de salud del sistema

### **Backup y Recuperación:**
- Point-in-time recovery en DynamoDB
- Backup automático de S3
- Estrategia de disaster recovery
- Versionado de código con CI/CD

---

---

## 📸 Gestión Completa de Imágenes

### **🔄 Flujo de Trabajo para Imágenes**

#### **1. Subida de Imágenes de Usuario:**
1. **App solicita URL firmada** → `POST /images/upload-url`
2. **Lambda genera URL firmada** con metadata
3. **App sube directamente a S3** usando URL firmada
4. **S3 Event trigger** → `image-processor` Lambda
5. **Procesamiento automático**: thumbnails, redimensionado
6. **Actualización DynamoDB** con URLs finales

#### **2. Servir Imágenes:**
1. **App solicita imagen** → URL desde DynamoDB
2. **CloudFront CDN** sirve imagen optimizada
3. **Cache inteligente** según tipo de contenido

#### **3. Eliminación de Imágenes:**
1. **App solicita eliminación** → `DELETE /images/{path}`
2. **Lambda elimina de S3** y actualiza DynamoDB
3. **CloudFront invalidation** para cache

### **📁 Ejemplos de Rutas Completas**

#### **Imágenes de Árboles (App Assets):**
```
https://d456def.cloudfront.net/trees/roble/semilla.png
https://d456def.cloudfront.net/trees/cerezo/arbolFlorecido.png
https://d456def.cloudfront.net/backgrounds/tree-backgrounds/sunset.jpg
```

#### **Contenido de Usuario:**
```
https://d123abc.cloudfront.net/avatars/user-123/profile.jpg
https://d123abc.cloudfront.net/couples/456/places/789/main.jpg
https://d123abc.cloudfront.net/couples/456/lifeplan/001/cover.jpg
```

### **🛠️ Configuración de Procesamiento**

#### **Tamaños de Imagen Estándar:**
- **Avatar**: 200x200px (original), 50x50px (thumbnail)
- **Lugares**: 800x600px (main), 200x150px (thumbnail)
- **Plan de Vida**: 1200x800px (cover), 400x300px (thumbnail)
- **Chat**: 800x600px (original), 200x150px (thumbnail)

#### **Formatos Soportados:**
- **Input**: JPG, PNG, WEBP
- **Output**: JPG (fotos), PNG (transparencias), WEBP (optimizado)

---

---

## 📊 RESUMEN EJECUTIVO - ARQUITECTURA OPTIMIZADA

### **🏆 ARQUITECTURA FINAL RECOMENDADA:**

#### **� Compute Layer:**
- **4 Lambda Functions** (consolidadas de 16)
- **API Gateway Optimizado** (integración nativa con Cognito)
- **AWS Cognito** (autenticación)

#### **� Data Layer:**
- **DynamoDB Optimizado** (single table design)
- **S3 Standard-IA** (almacenamiento optimizado)
- **Patrones de acceso optimizados** (PK/SK eficientes)

#### **🌐 CDN & Networking:**
- **CloudFlare CDN** (reemplaza CloudFront)
- **SSL automático** (certificados gratuitos)
- **DDoS protection** (incluido)

#### **� Servicios Externos:**
- **Firebase FCM** (reemplaza SNS)
- **Grafana Cloud** (reemplaza CloudWatch)

### **💰 COMPARACIÓN DE COSTOS FINAL:**

| Componente | Arquitectura Original | Arquitectura Optimizada | Ahorro |
|------------|----------------------|-------------------------|--------|
| **Compute** | 16 Lambdas ($60) | 4 Lambdas ($20) | **$40** |
| **Base de Datos** | DynamoDB ($100) | DynamoDB Optimizado ($6) | **$94** |
| **API Layer** | API Gateway ($35) | API Gateway Optimizado ($4) | **$31** |
| **CDN** | CloudFront ($20) | CloudFlare ($0) | **$20** |
| **Storage** | S3 Standard ($25) | S3 Standard-IA ($15) | **$10** |
| **Notificaciones** | SNS ($5) | Firebase FCM ($0) | **$5** |
| **Monitoreo** | CloudWatch ($15) | Grafana Cloud ($0) | **$15** |
| **Autenticación** | Cognito ($20) | Cognito ($20) | **$0** |
| **Requests** | S3 Requests ($15) | Optimizado ($5) | **$10** |
| **TOTAL** | **$295/mes** | **$70/mes** | **$225/mes** |

### **🎯 BENEFICIOS DE LA OPTIMIZACIÓN:**

#### **💰 Económicos:**
- **76% reducción de costos**: $295 → $70
- **ROI inmediato**: $225/mes de ahorro
- **Escalabilidad económica**: Costos predecibles

#### **🔧 Técnicos:**
- **Simplicidad**: 16 → 4 Lambdas
- **Mantenimiento**: Menos servicios que gestionar
- **Performance**: Latencia mejorada con ALB
- **Flexibilidad**: SQL completo vs DynamoDB limitado

#### **📈 Operacionales:**
- **Monitoreo gratuito**: Grafana Cloud
- **Alertas incluidas**: Sin costos adicionales
- **Backup automático**: RDS incluye PITR
- **Menos vendor lock-in**: Más portabilidad

### **⚡ FUNCIONALIDADES MANTENIDAS AL 100%:**
✅ **Sistema de árbol** con 6 etapas
✅ **Preguntas diarias** con historial
✅ **Plan de vida** CRUD completo
✅ **Lugares especiales** con GPS
✅ **Chat en tiempo real** con WebSocket
✅ **Ubicación compartida** en tiempo real
✅ **Notificaciones push** mejoradas
✅ **Estadísticas y rachas** completas
✅ **Gestión de imágenes** optimizada
✅ **Autenticación segura** con Cognito

---

**🎉 RESULTADO FINAL:**
**Arquitectura 70% más económica manteniendo 100% de funcionalidad**

**📊 Métricas Finales:**
- **💰 Costo**: $70/mes (vs $295/mes)
- **⚡ Lambdas**: 4 (vs 16)
- **🗄️ Base de Datos**: DynamoDB Optimizado
- **🌐 CDN**: CloudFlare gratuito
- **📱 Push**: Firebase FCM gratuito
- **📊 Monitoreo**: Grafana Cloud gratuito

---

## 🗺️ INTEGRACIÓN GOOGLE MAPS - ESPECIFICACIÓN COMPLETA

### **📍 ARQUITECTURA DE GEOLOCALIZACIÓN**

#### **🔄 Flujo de Geolocalización:**
```
Frontend App → Google Maps API → Mostrar ubicaciones en tiempo real
     ↓
Backend AWS ← Consulta lugares guardados ← DynamoDB (tabla principal)
     ↓
Frontend ← Coordenadas (lat/lng) ← Lambda Function
     ↓
Google Maps ← Renderizar marcadores ← Frontend
```

#### **🌐 Servicios Involucrados:**
1. **Google Maps JavaScript API** - Renderizado de mapas (Frontend)
2. **Google Maps Distance Matrix API** - Cálculo de distancias (Frontend)
3. **AWS Lambda** - Consulta de lugares guardados (Backend)
4. **DynamoDB** - Almacenamiento de coordenadas (Backend)
5. **Cognito** - Autenticación para acceso a lugares

### **📱 FUNCIONALIDADES DE GEOLOCALIZACIÓN:**

#### **1. 📍 Ubicación en Tiempo Real (Solo Frontend - Sin AWS)**
- **Obtención de ubicación**: `navigator.geolocation.getCurrentPosition()`
- **Mostrar ubicación actual**: Marcador azul en el mapa
- **Ubicación de la pareja**: Marcador diferente (si está compartiendo)
- **Cálculo de distancia**: Google Maps Distance Matrix API
- **Sin almacenamiento**: Solo visualización en tiempo real

#### **2. 🏠 Lugares Especiales (AWS + Google Maps)**
- **Botón "Mostrar nuestros lugares"**: Consulta a DynamoDB vía Lambda
- **Renderizado de marcadores**: Google Maps con coordenadas de DynamoDB
- **Categorías de lugares**: Íconos diferentes por tipo
- **Info windows**: Detalles del lugar con imágenes de S3

### **🗄️ ESTRUCTURA DYNAMODB PARA LUGARES**

#### **📊 Patrón de Acceso para Lugares:**
```
PK: COUPLE#{coupleId}
SK: PLACE#{placeId}

Ejemplo:
PK: COUPLE#456
SK: PLACE#789
```

#### **📋 Estructura Completa del Lugar:**
```json
{
  "PK": "COUPLE#456",
  "SK": "PLACE#789",
  "placeId": "789",
  "name": "Nuestro primer café",
  "description": "Donde tuvimos nuestra primera cita",
  "category": "cafe",
  "latitude": 19.4326,
  "longitude": -99.1332,
  "address": "Calle Principal 123, Ciudad",
  "mainImageUrl": "couples/456/places/789/main.jpg",
  "thumbnailUrl": "couples/456/places/789/thumb.jpg",
  "galleryUrls": [
    "couples/456/places/789/gallery/img1.jpg",
    "couples/456/places/789/gallery/img2.jpg"
  ],
  "visitedAt": "2024-01-01T00:00:00Z",
  "createdAt": "2024-01-02T00:00:00Z",
  "isFavorite": true,
  "notes": "Aquí nos conocimos mejor",
  "rating": 5,
  "GSI1PK": "TYPE#PLACE",
  "GSI1SK": "COUPLE#456#CREATED#2024-01-02"
}
```

### **⚡ LAMBDA FUNCTION - LUGARES ESPECIALES**

#### **🔧 Consulta de Lugares (GET /places):**
```javascript
// Dentro de hopie-api-handler
async function getPlaces(coupleId) {
  const params = {
    TableName: process.env.DYNAMODB_TABLE_NAME,
    KeyConditionExpression: 'PK = :pk AND begins_with(SK, :sk)',
    ExpressionAttributeValues: {
      ':pk': `COUPLE#${coupleId}`,
      ':sk': 'PLACE#'
    }
  };

  const result = await dynamodb.query(params).promise();

  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    },
    body: JSON.stringify({
      places: result.Items.map(item => ({
        id: item.placeId,
        name: item.name,
        description: item.description,
        category: item.category,
        latitude: item.latitude,
        longitude: item.longitude,
        address: item.address,
        mainImageUrl: item.mainImageUrl ? `${process.env.CDN_URL}/${item.mainImageUrl}` : null,
        thumbnailUrl: item.thumbnailUrl ? `${process.env.CDN_URL}/${item.thumbnailUrl}` : null,
        isFavorite: item.isFavorite || false,
        visitedAt: item.visitedAt,
        createdAt: item.createdAt,
        notes: item.notes,
        rating: item.rating
      }))
    })
  };
}
```

### **🔗 API ENDPOINTS - LUGARES ESPECIALES**

#### **📋 Endpoints para Google Maps:**
```yaml
GET /api/v1/places:
  description: Obtener todos los lugares guardados de la pareja
  auth: Cognito JWT
  response:
    places: Array[
      {
        id: string,
        name: string,
        latitude: number,
        longitude: number,
        category: string,
        address: string,
        thumbnailUrl: string
      }
    ]

POST /api/v1/places:
  description: Guardar nuevo lugar especial
  auth: Cognito JWT
  body:
    name: string (required)
    latitude: number (required)
    longitude: number (required)
    category: string (cafe|restaurant|park|home|other)
    address: string
    description: string
    notes: string

PUT /api/v1/places/{placeId}:
  description: Actualizar lugar existente
  auth: Cognito JWT

DELETE /api/v1/places/{placeId}:
  description: Eliminar lugar
  auth: Cognito JWT
```

### **🎨 FRONTEND - INTEGRACIÓN GOOGLE MAPS**

#### **📱 Flujo de "Mostrar nuestros lugares":**
```javascript
// 1. Usuario hace clic en botón "Mostrar nuestros lugares"
async function showOurPlaces() {
  try {
    // 2. Consultar lugares guardados en DynamoDB vía Lambda
    const response = await fetch('/api/v1/places', {
      headers: {
        'Authorization': `Bearer ${cognitoToken}`,
        'Content-Type': 'application/json'
      }
    });

    const data = await response.json();

    // 3. Renderizar marcadores en Google Maps
    data.places.forEach(place => {
      const marker = new google.maps.Marker({
        position: { lat: place.latitude, lng: place.longitude },
        map: googleMap,
        title: place.name,
        icon: getPlaceIcon(place.category), // Ícono según categoría
        animation: google.maps.Animation.DROP
      });

      // 4. Info window con detalles del lugar
      const infoWindow = new google.maps.InfoWindow({
        content: `
          <div>
            <h3>${place.name}</h3>
            <p>${place.description || ''}</p>
            ${place.thumbnailUrl ? `<img src="${place.thumbnailUrl}" style="width:100px;height:75px;">` : ''}
          </div>
        `
      });

      marker.addListener('click', () => {
        infoWindow.open(googleMap, marker);
      });
    });

  } catch (error) {
    console.error('Error loading places:', error);
  }
}
```

### **🔧 CONFIGURACIÓN GOOGLE MAPS**

#### **📋 APIs Requeridas:**
```yaml
Google Cloud Console:
  Project: hopie-app-maps
  APIs_Enabled:
    - Maps JavaScript API (para renderizar mapas)
    - Distance Matrix API (para calcular distancias)

  API_Key_Restrictions:
    HTTP_Referrers:
      - https://hopie-app.com/*
      - https://*.hopie-app.com/*
      - http://localhost:3000/* # Desarrollo
```

#### **💰 Costos Google Maps (1000 usuarios activos):**
```
Maps JavaScript API: $7/1000 cargas = $7/mes
Distance Matrix API: $5/1000 requests = $3/mes
Total Google Maps: $10/mes
```

### **📋 VARIABLES DE ENTORNO ACTUALIZADAS**

#### **🔐 Lambda Environment Variables:**
```yaml
Environment:
  Variables:
    # Existentes
    DYNAMODB_TABLE_NAME: hopie-main-table
    CDN_URL: https://d123abc.cloudfront.net

    # Nuevas para Google Maps (si es necesario geocoding desde backend)
    GOOGLE_MAPS_API_KEY: !Ref GoogleMapsApiKey # Opcional
```

#### **🌐 Frontend Environment Variables:**
```bash
# Existentes
REACT_APP_API_BASE_URL=https://api.hopie-app.com/v1
REACT_APP_CDN_URL=https://d123abc.cloudfront.net

# Nuevas para Google Maps
REACT_APP_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
```

### **📊 COSTOS TOTALES ACTUALIZADOS**

| Servicio | Costo Optimizado | Google Maps | Total |
|----------|------------------|-------------|-------|
| **AWS Stack** | $70/mes | - | $70 |
| **Google Maps** | - | $10/mes | $10 |
| **TOTAL FINAL** | **$70/mes** | **$10/mes** | **$80/mes** |

**🎉 COSTO FINAL: $80/mes (vs $295/mes original) = 73% de ahorro**

### **🔄 FLUJO COMPLETO DE GEOLOCALIZACIÓN**

#### **📱 Secuencia de Eventos:**
1. **App carga**: Google Maps se inicializa
2. **Ubicación actual**: `navigator.geolocation` obtiene coordenadas
3. **Mostrar en mapa**: Marcador azul para usuario actual
4. **Ubicación pareja**: Si está compartiendo, marcador diferente
5. **Botón "Mostrar lugares"**: Usuario hace clic
6. **Consulta AWS**: Lambda consulta DynamoDB tabla principal
7. **Respuesta coordenadas**: Lambda devuelve lat/lng de lugares
8. **Renderizar marcadores**: Google Maps muestra lugares especiales
9. **Interacción**: Usuario puede hacer clic en marcadores para detalles

#### **🎯 Resultado Final:**
- **Ubicación en tiempo real**: Solo frontend (Google Maps + GPS)
- **Lugares especiales**: Backend AWS (DynamoDB) + Frontend (Google Maps)
- **Cálculo distancias**: Solo frontend (Google Maps Distance Matrix)
- **Máximo desacoplamiento**: Google Maps para UI, AWS para datos
