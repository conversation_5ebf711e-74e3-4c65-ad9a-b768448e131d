Mi idea es la siguiente:

Tengo una aplicación cuyo objetivo es crear una experiencia en parejas. En un principio tengo algunas ideas principales:

-La sección principal, la cual es como que una especie de árbol. Cuando nomas inicia sesión van a ver unas secciones bloqueadas, y en las opciones de configuración le va a aparecer su ID de usuario, el cual es un ID Unico que identifica a ese usuario y que posteriormente en la pantalla principal tambien le tiene que aparecer el mensaje de Conectar con pareja y ahi va a tener que colocar el ID Unico de la otra persona. Y luego que diga que ya esta conectados. Pero en si en a pantalla principal todavia no decido bien si quiero mostrar como tal una sección de pregunta Diaria en la que las dos personas tiene que contestar y de fondo el arbol. El ARBOL ES una seccion muy IMPORTANTE, ya que justo despues de que se conecte con la otra pareja van a tener que eligir un arbol dentro de 5 especies u opciones que yo le voy a dar, el cual van a tener que regar diariamente, en si el arbol va a ir creciendo dependiendo de esa mecanica de regado. Pero con el tiempo y con forme vayan regando en un inicio va a ser solo la tierra y las semillas y poco a poco se va a ir creciendo y floreciendo. Y dependiendo de ciertos dias de regar van a ir como tal subiendo de nivel. Con cada nivel que vayan subiendo van a ganar recompensas las cuales pueden utilizar para (PENDIENTE DEFINIR PARA QUE SE PUEDE UTILIZAR PERO TIENE QUE SER ALGO QUE DESBLOQUEE ALGO O COMO UNA SECCION DE PREGUNTAS MAS INTIMAS O ALGO ASI, AUN ESTA PENDIENTE DEFINIR ESO PERO ESA SERA LA MECANICA COMO PARA QUE SE SIENTA ALGUN TIPO DE PROGRESO)
-Hay una sección de Nuestro plan de vida, el cual cuanta con como una plantilla de 5 etapas genericas que todo plan de vida en pareja tiene pero de igual manejra se va a poder editar para agregar o quitar etapas de modo de que se pueda personalizar, ahi se va a agregar como tal el nombre de la etapa, las metas o cosas que se esperan de esa etapa, la fecha estimada o aprox y un comentario adicional. 
-Hay una seccion de nuestros lugares especiales  en donde se va a poder colocar el nombre del lugar, lo que representa, maximo 3 fotos del momento o el lugar  que es opcional y una seccion en donde va a tener que seleccionar en el mapa el punto exacto en donde ocurrio. Y en esa misma pantalla o seccion que haya la opcion para poner el mapa y que ahi se muestren todos los puntos importantes o que tenga registrado la pareja de modo que cuando se le presione al punto lo regrese a la pantalla y se le despliegue la descripcion del momento y todo el card que ya lleno descibiendo el lugar. 
-Va a existir una seccion que va a ser un chat un poco simple en el que se puedan comunicar, mandar fotos y gifs. 
-Tengo una especie de geolocalizador o mapa el cual muestra en tiempo real con una imagen(que va a ser la foto de perfil de su cuenta de gmail ya que con esa va a iniciar sesión) donde está cada uno de los dos.
-Una sección de sugerencias o comentarios para agregar nuevas cosas o mejorar las existentes.
-Esta tambien la seccion de configuracion que es otra pantalla tambien.


Ayudame con eso ya que yo he identificado algunas entidades o posibles tablas, en si yo no estoy seguro como implementarlo totalmente todo el sistema porque yo quiero hacer algo lo mas desacoplado posible con cosas que sean muy reutilizables. Yo quiero manejar todo mi backend con aws, quiero manejar las cosas con api gateway y lambdas. Yo tengo como tal algunas entidades definidas, que son estas:
-Usuarios -TipoUsuario -Chat(mensajes)  - preguntas  - DetalleRespuestas  -PlanVidaEtapas  -LugaresEspeciales - NivelProgreso -ProgresoArbol(Para determinar el tamaño o dibujo)  - EstadisticasDelArbol(QuienARegadoCadaDia, como el historial de eso). -DetalleSugerenciasComentarios.


EL problema es que yo estoy pensando actualmente en intentar manejarlo todo a traves de apis y mi base de datos con dynamoDB. de modo que sea nosql pero de igual manera no se como se estructuraria en ese caso mis tablas porque en si si tiene que existir algunas relaciones asi como en los detalles. Y no se como se puede modelar esa parte. Yo necesito que me ayudes a modelar todo esto en tablas como si fuera un sql y aparte como si fuera un nosql con dynamodb. 










