#!/bin/bash

TABLE_NAME="HopieApp"

# 1. Insertar usuarios
aws dynamodb put-item \
    --table-name $TABLE_NAME \
    --item '{
        "PK": {"S": "USER#USER1"},
        "SK": {"S": "METADATA"},
        "userId": {"S": "USER1"},
        "email": {"S": "<EMAIL>"},
        "name": {"S": "Ana"},
        "profilePic": {"S": "https://example.com/profiles/ana.jpg"},
        "connectionCode": {"S": "ABC123"},
        "createdAt": {"S": "2023-11-01T10:00:00Z"}
    }'

aws dynamodb put-item \
    --table-name $TABLE_NAME \
    --item '{
        "PK": {"S": "USER#USER2"},
        "SK": {"S": "METADATA"},
        "userId": {"S": "USER2"},
        "email": {"S": "<EMAIL>"},
        "name": {"S": "<PERSON>"},
        "profilePic": {"S": "https://example.com/profiles/luis.jpg"},
        "connectionCode": {"S": "XYZ789"},
        "createdAt": {"S": "2023-11-01T10:05:00Z"}
    }'

# 2. Crear pareja conectada
aws dynamodb put-item \
    --table-name $TABLE_NAME \
    --item '{
        "PK": {"S": "COUPLE#COUPLE1"},
        "SK": {"S": "METADATA"},
        "coupleId": {"S": "COUPLE1"},
        "user1Id": {"S": "USER1"},
        "user2Id": {"S": "USER2"},
        "treeType": {"S": "roble"},
        "level": {"N": "1"},
        "experiencePoints": {"N": "0"},
        "createdAt": {"S": "2023-11-01T11:00:00Z"},
        "GSI1PK": {"S": "USER#USER1"},
        "GSI1SK": {"S": "COUPLE#COUPLE1"}
    }'

# Índice inverso para el segundo usuario
aws dynamodb put-item \
    --table-name $TABLE_NAME \
    --item '{
        "PK": {"S": "COUPLE#COUPLE1"},
        "SK": {"S": "USER#USER2"},
        "GSI1PK": {"S": "USER#USER2"},
        "GSI1SK": {"S": "COUPLE#COUPLE1"}
    }'

# 3. Progreso del árbol
aws dynamodb put-item \
    --table-name $TABLE_NAME \
    --item '{
        "PK": {"S": "COUPLE#COUPLE1"},
        "SK": {"S": "TREE#STATUS"},
        "currentStage": {"S": "semilla"},
        "lastWatered": {"S": "2023-11-01"},
        "wateringStreak": {"N": "1"}
    }'

# 4. Registros de riego
aws dynamodb put-item \
    --table-name $TABLE_NAME \
    --item '{
        "PK": {"S": "COUPLE#COUPLE1"},
        "SK": {"S": "WATER#2023-11-01"},
        "wateredBy": {"S": "USER1"},
        "pointsEarned": {"N": "10"},
        "timestamp": {"S": "2023-11-01T12:00:00Z"}
    }'

# 5. Plan de vida (etapas)
aws dynamodb put-item \
    --table-name $TABLE_NAME \
    --item '{
        "PK": {"S": "COUPLE#COUPLE1"},
        "SK": {"S": "LIFEPLAN#1"},
        "name": {"S": "Conocernos"},
        "goals": {"L": [{"S": "Salir 1 vez por semana"}, {"S": "Presentar a amigos"}]},
        "estimatedDate": {"S": "2023-12-31"},
        "completed": {"BOOL": false},
        "order": {"N": "1"}
    }'

aws dynamodb put-item \
    --table-name $TABLE_NAME \
    --item '{
        "PK": {"S": "COUPLE#COUPLE1"},
        "SK": {"S": "LIFEPLAN#2"},
        "name": {"S": "Compromiso"},
        "goals": {"L": [{"S": "Ahorrar para anillo"}, {"S": "Conocer familias"}]},
        "estimatedDate": {"S": "2024-06-01"},
        "completed": {"BOOL": false},
        "order": {"N": "2"}
    }'

# 6. Lugares especiales
aws dynamodb put-item \
    --table-name $TABLE_NAME \
    --item '{
        "PK": {"S": "COUPLE#COUPLE1"},
        "SK": {"S": "PLACE#LOC1"},
        "placeId": {"S": "LOC1"},
        "name": {"S": "Primera cita"},
        "description": {"S": "Donde nos conocimos en el café central"},
        "location": {"M": {
            "lat": {"N": "19.4326"},
            "long": {"N": "-99.1332"},
            "address": {"S": "Café Central, CDMX"}
        }},
        "photos": {"L": [{"S": "https://example.com/photos/loc1_1.jpg"}]},
        "createdAt": {"S": "2023-11-01T13:00:00Z"}
    }'

# 7. Mensajes de chat
aws dynamodb put-item \
    --table-name $TABLE_NAME \
    --item '{
        "PK": {"S": "COUPLE#COUPLE1"},
        "SK": {"S": "CHAT#20231101140000"},
        "messageId": {"S": "MSG1"},
        "senderId": {"S": "USER1"},
        "content": {"S": "Hola cariño! ¿Cómo estás?"},
        "timestamp": {"S": "2023-11-01T14:00:00Z"},
        "GSI1PK": {"S": "USER#USER1"},
        "GSI1SK": {"S": "CHAT#20231101140000"}
    }'

aws dynamodb put-item \
    --table-name $TABLE_NAME \
    --item '{
        "PK": {"S": "COUPLE#COUPLE1"},
        "SK": {"S": "CHAT#20231101140500"},
        "messageId": {"S": "MSG2"},
        "senderId": {"S": "USER2"},
        "content": {"S": "Hola amor! Bien, ¿y tú?"},
        "timestamp": {"S": "2023-11-01T14:05:00Z"},
        "GSI1PK": {"S": "USER#USER2"},
        "GSI1SK": {"S": "CHAT#20231101140500"}
    }'

# 8. Preguntas diarias
aws dynamodb put-item \
    --table-name $TABLE_NAME \
    --item '{
        "PK": {"S": "QUESTION#2023-11-01"},
        "SK": {"S": "COUPLE#COUPLE1"},
        "questionText": {"S": "¿Cómo describirías tu día?"},
        "category": {"S": "daily"},
        "answers": {"L": [
            {"M": {
                "userId": {"S": "USER1"},
                "text": {"S": "Fue un gran día!"},
                "timestamp": {"S": "2023-11-01T20:00:00Z"}
            }},
            {"M": {
                "userId": {"S": "USER2"},
                "text": {"S": "Estresante pero productivo"},
                "timestamp": {"S": "2023-11-01T21:00:00Z"}
            }}
        ]},
        "GSI1PK": {"S": "COUPLE#COUPLE1"},
        "GSI1SK": {"S": "QUESTION#2023-11-01"}
    }'

# 9. Ubicación en tiempo real
aws dynamodb put-item \
    --table-name $TABLE_NAME \
    --item '{
        "PK": {"S": "USER#USER1"},
        "SK": {"S": "LOCATION#LIVE"},
        "lat": {"N": "19.4326"},
        "long": {"N": "-99.1332"},
        "timestamp": {"S": "2023-11-01T15:30:00Z"},
        "expiresAt": {"N": "1730419200"}  
    }'

aws dynamodb put-item \
    --table-name $TABLE_NAME \
    --item '{
        "PK": {"S": "USER#USER2"},
        "SK": {"S": "LOCATION#LIVE"},
        "lat": {"N": "19.4330"},
        "long": {"N": "-99.1340"},
        "timestamp": {"S": "2023-11-01T15:35:00Z"},
        "expiresAt": {"N": "1730419200"}
    }'

# 10. Sugerencias
aws dynamodb put-item \
    --table-name $TABLE_NAME \
    --item '{
        "PK": {"S": "SUGGESTION#SUG1"},
        "SK": {"S": "USER#USER1"},
        "suggestionId": {"S": "SUG1"},
        "text": {"S": "Podrían añadir más tipos de árboles"},
        "status": {"S": "pending"},
        "createdAt": {"S": "2023-11-01T16:00:00Z"}
    }'

echo "Datos de prueba insertados correctamente en la tabla $TABLE_NAME"