# Hopie - Creciendo Juntos 💕

Una aplicación móvil diseñada para ayudar a las parejas a fortalecer su relación, documentar momentos especiales y crecer juntos día a día.

## 🌟 Características Principales

### 🌳 Árbol del Amor
- **Crecimiento diario**: Riega el árbol todos los días para verlo crecer
- **5 especies diferentes**: <PERSON><PERSON>, Cerezo, Pino, Sauce y Maple
- **Sistema de niveles**: Gana experiencia y desbloquea recompensas
- **Progreso visual**: Ve cómo evoluciona desde semilla hasta árbol florecido

### 💑 Conexión de Parejas
- **Código único**: Cada usuario tiene un ID único para conectarse
- **Sincronización**: Toda la información se comparte entre la pareja
- **Estado en tiempo real**: Ve el progreso conjunto

### 📋 Plan de Vida
- **Plantillas predefinidas**: 5 etapas básicas de la vida en pareja
- **Personalizable**: <PERSON><PERSON><PERSON><PERSON>, edita o elimina etapas según sus necesidades
- **Seguimiento de metas**: Define objetivos específicos para cada etapa
- **Fechas estimadas**: Planifica su futuro juntos

### 📍 Lugares Especiales
- **Documentación de momentos**: Guarda lugares importantes de su relación
- **Fotos y descripciones**: Hasta 3 fotos por lugar con historias detalladas
- **Ubicación en mapa**: Marca la ubicación exacta de cada lugar
- **Mapa interactivo**: Ve todos sus lugares especiales en un solo mapa

### 💬 Chat de Pareja
- **Mensajes en tiempo real**: Comunicación directa entre la pareja
- **Multimedia**: Envía fotos y GIFs
- **Estado de lectura**: Ve cuando tu pareja ha leído los mensajes

### 📍 Ubicación en Tiempo Real
- **Compartir ubicación**: Ve dónde está tu pareja en tiempo real
- **Privacidad**: Control total sobre cuándo compartir la ubicación
- **Mapa en vivo**: Visualización en tiempo real con fotos de perfil

### ❓ Preguntas Diarias
- **Conexión profunda**: Preguntas diseñadas para conocerse mejor
- **Categorías variadas**: Diarias, íntimas, futuro, recuerdos y sueños
- **Respuestas compartidas**: Ambos deben responder para ver las respuestas del otro

### 💡 Sugerencias y Feedback
- **Mejora continua**: Envía ideas para mejorar la aplicación
- **Seguimiento**: Ve el estado de tus sugerencias
- **Respuestas del equipo**: Recibe feedback sobre tus ideas

## 🎨 Diseño

### Paleta de Colores
- **Primario**: #b4d5e5 (Azul suave)
- **Secundario**: #cad8eb (Azul claro)
- **Acento**: #bcc3e5 (Lavanda)
- **Terciario**: #abcede (Azul cielo)
- **Cuaternario**: #c5ebe4 (Verde menta)

### Características de UI/UX
- **Material Design 3**: Interfaz moderna y consistente
- **Animaciones suaves**: Transiciones fluidas entre pantallas
- **Responsive**: Adaptable a diferentes tamaños de pantalla
- **Accesible**: Diseño inclusivo y fácil de usar

## 🏗️ Arquitectura Técnica

### Frontend (Flutter)
- **Framework**: Flutter 3.7.2+
- **Lenguaje**: Dart
- **Gestión de estado**: Provider
- **Navegación**: Navigator 2.0

### Backend (AWS)
- **API**: AWS API Gateway
- **Funciones**: AWS Lambda
- **Base de datos**: DynamoDB
- **Autenticación**: AWS Cognito con Google Sign-In
- **Almacenamiento**: S3 para fotos

## 📱 Pantallas Implementadas

✅ **Completadas:**
1. Splash Screen con animaciones
2. Login con Google Sign-In
3. Pantalla de conexión de parejas
4. Árbol principal con sistema de riego
5. Selección de especies de árboles
6. Plan de vida con plantillas
7. Lugares especiales con mapas
8. Chat en tiempo real
9. Ubicación compartida
10. Configuración y perfil
11. Sistema de sugerencias

## 🚀 Cómo Ejecutar

```bash
# Instalar dependencias
flutter pub get

# Ejecutar en modo debug
flutter run

# Construir para producción
flutter build apk
```

## 🔧 Configuración Pendiente

Para conectar con tu backend de AWS:

1. **Actualizar API_URL** en `lib/services/api_service.dart`
2. **Configurar Google Sign-In** con tus credenciales
3. **Configurar AWS Cognito** para autenticación
4. **Configurar Google Maps API** para los mapas

## 📊 Estado del Proyecto

- ✅ **UI/UX**: 100% completado
- ✅ **Navegación**: 100% completado
- ✅ **Modelos de datos**: 100% completado
- ✅ **Servicios de API**: 100% preparado
- ⏳ **Integración backend**: Pendiente de configuración
- ⏳ **Testing**: Pendiente

---

**Hopie** - Porque el amor crece día a día 💕🌱
