class ChatMessage {
  final String messageId;
  final String coupleId;
  final String senderId;
  final String content;
  final MessageType type;
  final DateTime timestamp;
  final bool isRead;
  final String? mediaUrl;

  ChatMessage({
    required this.messageId,
    required this.coupleId,
    required this.senderId,
    required this.content,
    required this.type,
    required this.timestamp,
    required this.isRead,
    this.mediaUrl,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      messageId: json['messageId'] ?? '',
      coupleId: json['coupleId'] ?? '',
      senderId: json['senderId'] ?? '',
      content: json['content'] ?? '',
      type: MessageType.values.firstWhere(
        (type) => type.name == json['type'],
        orElse: () => MessageType.text,
      ),
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      isRead: json['isRead'] ?? false,
      mediaUrl: json['mediaUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'messageId': messageId,
      'coupleId': coupleId,
      'senderId': senderId,
      'content': content,
      'type': type.name,
      'timestamp': timestamp.toIso8601String(),
      'isRead': isRead,
      'mediaUrl': mediaUrl,
    };
  }

  ChatMessage copyWith({
    String? messageId,
    String? coupleId,
    String? senderId,
    String? content,
    MessageType? type,
    DateTime? timestamp,
    bool? isRead,
    String? mediaUrl,
  }) {
    return ChatMessage(
      messageId: messageId ?? this.messageId,
      coupleId: coupleId ?? this.coupleId,
      senderId: senderId ?? this.senderId,
      content: content ?? this.content,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      mediaUrl: mediaUrl ?? this.mediaUrl,
    );
  }

  bool get hasMedia => mediaUrl != null && mediaUrl!.isNotEmpty;
}

enum MessageType {
  text,
  image,
  gif;

  String get displayName {
    switch (this) {
      case MessageType.text:
        return 'Texto';
      case MessageType.image:
        return 'Imagen';
      case MessageType.gif:
        return 'GIF';
    }
  }
}
