class Couple {
  final String coupleId;
  final String user1Id;
  final String user2Id;
  final String treeType;
  final int level;
  final int experiencePoints;
  final DateTime createdAt;

  Couple({
    required this.coupleId,
    required this.user1Id,
    required this.user2Id,
    required this.treeType,
    required this.level,
    required this.experiencePoints,
    required this.createdAt,
  });

  factory Couple.fromJson(Map<String, dynamic> json) {
    return Couple(
      coupleId: json['coupleId'] ?? '',
      user1Id: json['user1Id'] ?? '',
      user2Id: json['user2Id'] ?? '',
      treeType: json['treeType'] ?? 'roble',
      level: json['level'] ?? 1,
      experiencePoints: json['experiencePoints'] ?? 0,
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'coupleId': coupleId,
      'user1Id': user1Id,
      'user2Id': user2Id,
      'treeType': treeType,
      'level': level,
      'experiencePoints': experiencePoints,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  Couple copyWith({
    String? coupleId,
    String? user1Id,
    String? user2Id,
    String? treeType,
    int? level,
    int? experiencePoints,
    DateTime? createdAt,
  }) {
    return Couple(
      coupleId: coupleId ?? this.coupleId,
      user1Id: user1Id ?? this.user1Id,
      user2Id: user2Id ?? this.user2Id,
      treeType: treeType ?? this.treeType,
      level: level ?? this.level,
      experiencePoints: experiencePoints ?? this.experiencePoints,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

enum TreeType {
  roble,
  cerezo,
  pino,
  sauce,
  maple;

  String get displayName {
    switch (this) {
      case TreeType.roble:
        return 'Roble';
      case TreeType.cerezo:
        return 'Cerezo';
      case TreeType.pino:
        return 'Pino';
      case TreeType.sauce:
        return 'Sauce';
      case TreeType.maple:
        return 'Maple';
    }
  }

  String get description {
    switch (this) {
      case TreeType.roble:
        return 'Fuerte y resistente, simboliza la estabilidad';
      case TreeType.cerezo:
        return 'Hermoso y delicado, representa la belleza del amor';
      case TreeType.pino:
        return 'Siempre verde, simboliza la constancia';
      case TreeType.sauce:
        return 'Flexible y adaptable, representa la comprensión';
      case TreeType.maple:
        return 'Colorido y vibrante, simboliza la pasión';
    }
  }
}
