class LifePlan {
  final String id;
  final String coupleId;
  final String name;
  final List<String> goals;
  final DateTime? estimatedDate;
  final bool completed;
  final int order;
  final String? additionalComments;
  final DateTime createdAt;
  final DateTime? completedAt;

  LifePlan({
    required this.id,
    required this.coupleId,
    required this.name,
    required this.goals,
    this.estimatedDate,
    required this.completed,
    required this.order,
    this.additionalComments,
    required this.createdAt,
    this.completedAt,
  });

  factory LifePlan.fromJson(Map<String, dynamic> json) {
    return LifePlan(
      id: json['id'] ?? '',
      coupleId: json['coupleId'] ?? '',
      name: json['name'] ?? '',
      goals: List<String>.from(json['goals'] ?? []),
      estimatedDate: json['estimatedDate'] != null 
          ? DateTime.parse(json['estimatedDate']) 
          : null,
      completed: json['completed'] ?? false,
      order: json['order'] ?? 1,
      additionalComments: json['additionalComments'],
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      completedAt: json['completedAt'] != null 
          ? DateTime.parse(json['completedAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'coupleId': coupleId,
      'name': name,
      'goals': goals,
      'estimatedDate': estimatedDate?.toIso8601String(),
      'completed': completed,
      'order': order,
      'additionalComments': additionalComments,
      'createdAt': createdAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
    };
  }

  LifePlan copyWith({
    String? id,
    String? coupleId,
    String? name,
    List<String>? goals,
    DateTime? estimatedDate,
    bool? completed,
    int? order,
    String? additionalComments,
    DateTime? createdAt,
    DateTime? completedAt,
  }) {
    return LifePlan(
      id: id ?? this.id,
      coupleId: coupleId ?? this.coupleId,
      name: name ?? this.name,
      goals: goals ?? this.goals,
      estimatedDate: estimatedDate ?? this.estimatedDate,
      completed: completed ?? this.completed,
      order: order ?? this.order,
      additionalComments: additionalComments ?? this.additionalComments,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
    );
  }
}

class LifePlanTemplate {
  final String name;
  final List<String> defaultGoals;
  final String description;

  LifePlanTemplate({
    required this.name,
    required this.defaultGoals,
    required this.description,
  });

  static List<LifePlanTemplate> getDefaultTemplates() {
    return [
      LifePlanTemplate(
        name: 'Conocernos',
        defaultGoals: [
          'Salir en citas regulares',
          'Presentar a amigos y familia',
          'Descubrir intereses comunes',
          'Establecer comunicación abierta'
        ],
        description: 'La etapa inicial donde se conocen profundamente',
      ),
      LifePlanTemplate(
        name: 'Compromiso',
        defaultGoals: [
          'Hablar sobre el futuro juntos',
          'Conocer a las familias',
          'Planificar la propuesta',
          'Ahorrar para el anillo'
        ],
        description: 'Decidir formalizar la relación',
      ),
      LifePlanTemplate(
        name: 'Matrimonio',
        defaultGoals: [
          'Planificar la boda',
          'Elegir el lugar y fecha',
          'Organizar luna de miel',
          'Preparar documentos legales'
        ],
        description: 'La celebración de su unión',
      ),
      LifePlanTemplate(
        name: 'Vida en pareja',
        defaultGoals: [
          'Encontrar hogar juntos',
          'Establecer rutinas compartidas',
          'Planificar finanzas conjuntas',
          'Crear tradiciones propias'
        ],
        description: 'Construir una vida juntos',
      ),
      LifePlanTemplate(
        name: 'Familia',
        defaultGoals: [
          'Decidir sobre tener hijos',
          'Preparar el hogar para la familia',
          'Planificar la educación',
          'Establecer valores familiares'
        ],
        description: 'Expandir la familia si así lo desean',
      ),
    ];
  }
}
