class Question {
  final String id;
  final String questionText;
  final QuestionCategory category;
  final DateTime date;
  final String coupleId;
  final List<QuestionAnswer> answers;

  Question({
    required this.id,
    required this.questionText,
    required this.category,
    required this.date,
    required this.coupleId,
    required this.answers,
  });

  factory Question.fromJson(Map<String, dynamic> json) {
    return Question(
      id: json['id'] ?? '',
      questionText: json['questionText'] ?? '',
      category: QuestionCategory.values.firstWhere(
        (cat) => cat.name == json['category'],
        orElse: () => QuestionCategory.daily,
      ),
      date: DateTime.parse(json['date'] ?? DateTime.now().toIso8601String()),
      coupleId: json['coupleId'] ?? '',
      answers: (json['answers'] as List<dynamic>? ?? [])
          .map((answer) => QuestionAnswer.fromJson(answer))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'questionText': questionText,
      'category': category.name,
      'date': date.toIso8601String(),
      'coupleId': coupleId,
      'answers': answers.map((answer) => answer.toJson()).toList(),
    };
  }

  Question copyWith({
    String? id,
    String? questionText,
    QuestionCategory? category,
    DateTime? date,
    String? coupleId,
    List<QuestionAnswer>? answers,
  }) {
    return Question(
      id: id ?? this.id,
      questionText: questionText ?? this.questionText,
      category: category ?? this.category,
      date: date ?? this.date,
      coupleId: coupleId ?? this.coupleId,
      answers: answers ?? this.answers,
    );
  }

  bool get isAnsweredByBoth => answers.length >= 2;
  
  QuestionAnswer? getAnswerByUser(String userId) {
    try {
      return answers.firstWhere((answer) => answer.userId == userId);
    } catch (e) {
      return null;
    }
  }
}

class QuestionAnswer {
  final String userId;
  final String text;
  final DateTime timestamp;

  QuestionAnswer({
    required this.userId,
    required this.text,
    required this.timestamp,
  });

  factory QuestionAnswer.fromJson(Map<String, dynamic> json) {
    return QuestionAnswer(
      userId: json['userId'] ?? '',
      text: json['text'] ?? '',
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'text': text,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

enum QuestionCategory {
  daily,
  intimate,
  future,
  memories,
  dreams;

  String get displayName {
    switch (this) {
      case QuestionCategory.daily:
        return 'Diaria';
      case QuestionCategory.intimate:
        return 'Íntima';
      case QuestionCategory.future:
        return 'Futuro';
      case QuestionCategory.memories:
        return 'Recuerdos';
      case QuestionCategory.dreams:
        return 'Sueños';
    }
  }

  String get description {
    switch (this) {
      case QuestionCategory.daily:
        return 'Preguntas sobre el día a día';
      case QuestionCategory.intimate:
        return 'Preguntas más profundas y personales';
      case QuestionCategory.future:
        return 'Preguntas sobre planes y metas';
      case QuestionCategory.memories:
        return 'Preguntas sobre recuerdos compartidos';
      case QuestionCategory.dreams:
        return 'Preguntas sobre sueños y aspiraciones';
    }
  }
}
