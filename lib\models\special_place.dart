class SpecialPlace {
  final String id;
  final String coupleId;
  final String name;
  final String description;
  final PlaceLocation location;
  final List<String> photos;
  final DateTime createdAt;

  SpecialPlace({
    required this.id,
    required this.coupleId,
    required this.name,
    required this.description,
    required this.location,
    required this.photos,
    required this.createdAt,
  });

  factory SpecialPlace.fromJson(Map<String, dynamic> json) {
    return SpecialPlace(
      id: json['id'] ?? '',
      coupleId: json['coupleId'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      location: PlaceLocation.fromJson(json['location'] ?? {}),
      photos: List<String>.from(json['photos'] ?? []),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'coupleId': coupleId,
      'name': name,
      'description': description,
      'location': location.toJson(),
      'photos': photos,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  SpecialPlace copyWith({
    String? id,
    String? coupleId,
    String? name,
    String? description,
    PlaceLocation? location,
    List<String>? photos,
    DateTime? createdAt,
  }) {
    return SpecialPlace(
      id: id ?? this.id,
      coupleId: coupleId ?? this.coupleId,
      name: name ?? this.name,
      description: description ?? this.description,
      location: location ?? this.location,
      photos: photos ?? this.photos,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

class PlaceLocation {
  final double latitude;
  final double longitude;
  final String address;

  PlaceLocation({
    required this.latitude,
    required this.longitude,
    required this.address,
  });

  factory PlaceLocation.fromJson(Map<String, dynamic> json) {
    return PlaceLocation(
      latitude: (json['lat'] ?? json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['long'] ?? json['longitude'] ?? 0.0).toDouble(),
      address: json['address'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'lat': latitude,
      'long': longitude,
      'address': address,
    };
  }

  PlaceLocation copyWith({
    double? latitude,
    double? longitude,
    String? address,
  }) {
    return PlaceLocation(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      address: address ?? this.address,
    );
  }
}
