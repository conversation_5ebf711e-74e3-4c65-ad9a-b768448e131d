class TreeProgress {
  final String coupleId;
  final TreeStage currentStage;
  final DateTime? lastWatered;
  final int wateringStreak;
  final int totalWaterings;

  TreeProgress({
    required this.coupleId,
    required this.currentStage,
    this.lastWatered,
    required this.wateringStreak,
    required this.totalWaterings,
  });

  factory TreeProgress.fromJson(Map<String, dynamic> json) {
    return TreeProgress(
      coupleId: json['coupleId'] ?? '',
      currentStage: TreeStage.values.firstWhere(
        (stage) => stage.name == json['currentStage'],
        orElse: () => TreeStage.semilla,
      ),
      lastWatered: json['lastWatered'] != null 
          ? DateTime.parse(json['lastWatered']) 
          : null,
      wateringStreak: json['wateringStreak'] ?? 0,
      totalWaterings: json['totalWaterings'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'coupleId': coupleId,
      'currentStage': currentStage.name,
      'lastWatered': lastWatered?.toIso8601String(),
      'wateringStreak': wateringStreak,
      'totalWaterings': totalWaterings,
    };
  }

  TreeProgress copyWith({
    String? coupleId,
    TreeStage? currentStage,
    DateTime? lastWatered,
    int? wateringStreak,
    int? totalWaterings,
  }) {
    return TreeProgress(
      coupleId: coupleId ?? this.coupleId,
      currentStage: currentStage ?? this.currentStage,
      lastWatered: lastWatered ?? this.lastWatered,
      wateringStreak: wateringStreak ?? this.wateringStreak,
      totalWaterings: totalWaterings ?? this.totalWaterings,
    );
  }

  bool get canWaterToday {
    if (lastWatered == null) return true;
    final today = DateTime.now();
    final lastWateredDate = DateTime(
      lastWatered!.year,
      lastWatered!.month,
      lastWatered!.day,
    );
    final todayDate = DateTime(today.year, today.month, today.day);
    return !lastWateredDate.isAtSameMomentAs(todayDate);
  }
}

enum TreeStage {
  semilla,
  brote,
  plantula,
  arbolJoven,
  arbolMaduro,
  arbolFlorecido;

  String get displayName {
    switch (this) {
      case TreeStage.semilla:
        return 'Semilla';
      case TreeStage.brote:
        return 'Brote';
      case TreeStage.plantula:
        return 'Plántula';
      case TreeStage.arbolJoven:
        return 'Árbol Joven';
      case TreeStage.arbolMaduro:
        return 'Árbol Maduro';
      case TreeStage.arbolFlorecido:
        return 'Árbol Florecido';
    }
  }

  String get description {
    switch (this) {
      case TreeStage.semilla:
        return 'El comienzo de su historia juntos';
      case TreeStage.brote:
        return 'Los primeros signos de crecimiento';
      case TreeStage.plantula:
        return 'Echando raíces profundas';
      case TreeStage.arbolJoven:
        return 'Creciendo fuertes juntos';
      case TreeStage.arbolMaduro:
        return 'Una relación sólida y estable';
      case TreeStage.arbolFlorecido:
        return 'El amor en plena floración';
    }
  }

  int get requiredWaterings {
    switch (this) {
      case TreeStage.semilla:
        return 0;
      case TreeStage.brote:
        return 7;
      case TreeStage.plantula:
        return 21;
      case TreeStage.arbolJoven:
        return 50;
      case TreeStage.arbolMaduro:
        return 100;
      case TreeStage.arbolFlorecido:
        return 200;
    }
  }
}

class WateringRecord {
  final String coupleId;
  final DateTime date;
  final String wateredBy;
  final int pointsEarned;
  final DateTime timestamp;

  WateringRecord({
    required this.coupleId,
    required this.date,
    required this.wateredBy,
    required this.pointsEarned,
    required this.timestamp,
  });

  factory WateringRecord.fromJson(Map<String, dynamic> json) {
    return WateringRecord(
      coupleId: json['coupleId'] ?? '',
      date: DateTime.parse(json['date'] ?? DateTime.now().toIso8601String()),
      wateredBy: json['wateredBy'] ?? '',
      pointsEarned: json['pointsEarned'] ?? 10,
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'coupleId': coupleId,
      'date': date.toIso8601String(),
      'wateredBy': wateredBy,
      'pointsEarned': pointsEarned,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}
