class User {
  final String userId;
  final String email;
  final String name;
  final String profilePic;
  final String connectionCode;
  final DateTime createdAt;
  final String? coupleId;

  User({
    required this.userId,
    required this.email,
    required this.name,
    required this.profilePic,
    required this.connectionCode,
    required this.createdAt,
    this.coupleId,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      userId: json['userId'] ?? '',
      email: json['email'] ?? '',
      name: json['name'] ?? '',
      profilePic: json['profilePic'] ?? '',
      connectionCode: json['connectionCode'] ?? '',
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      coupleId: json['coupleId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'email': email,
      'name': name,
      'profilePic': profilePic,
      'connectionCode': connectionCode,
      'createdAt': createdAt.toIso8601String(),
      'coupleId': coupleId,
    };
  }

  User copyWith({
    String? userId,
    String? email,
    String? name,
    String? profilePic,
    String? connectionCode,
    DateTime? createdAt,
    String? coupleId,
  }) {
    return User(
      userId: userId ?? this.userId,
      email: email ?? this.email,
      name: name ?? this.name,
      profilePic: profilePic ?? this.profilePic,
      connectionCode: connectionCode ?? this.connectionCode,
      createdAt: createdAt ?? this.createdAt,
      coupleId: coupleId ?? this.coupleId,
    );
  }
}
