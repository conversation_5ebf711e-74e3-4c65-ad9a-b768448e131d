class UserLocation {
  final String userId;
  final double latitude;
  final double longitude;
  final DateTime timestamp;
  final DateTime expiresAt;

  UserLocation({
    required this.userId,
    required this.latitude,
    required this.longitude,
    required this.timestamp,
    required this.expiresAt,
  });

  factory UserLocation.fromJson(Map<String, dynamic> json) {
    return UserLocation(
      userId: json['userId'] ?? '',
      latitude: (json['lat'] ?? json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['long'] ?? json['longitude'] ?? 0.0).toDouble(),
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      expiresAt: json['expiresAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['expiresAt'] * 1000)
          : DateTime.now().add(const Duration(hours: 1)),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'lat': latitude,
      'long': longitude,
      'timestamp': timestamp.toIso8601String(),
      'expiresAt': expiresAt.millisecondsSinceEpoch ~/ 1000,
    };
  }

  UserLocation copyWith({
    String? userId,
    double? latitude,
    double? longitude,
    DateTime? timestamp,
    DateTime? expiresAt,
  }) {
    return UserLocation(
      userId: userId ?? this.userId,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      timestamp: timestamp ?? this.timestamp,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }

  bool get isExpired => DateTime.now().isAfter(expiresAt);
}

class Suggestion {
  final String id;
  final String userId;
  final String text;
  final SuggestionStatus status;
  final DateTime createdAt;
  final DateTime? resolvedAt;
  final String? adminResponse;

  Suggestion({
    required this.id,
    required this.userId,
    required this.text,
    required this.status,
    required this.createdAt,
    this.resolvedAt,
    this.adminResponse,
  });

  factory Suggestion.fromJson(Map<String, dynamic> json) {
    return Suggestion(
      id: json['suggestionId'] ?? json['id'] ?? '',
      userId: json['userId'] ?? '',
      text: json['text'] ?? '',
      status: SuggestionStatus.values.firstWhere(
        (status) => status.name == json['status'],
        orElse: () => SuggestionStatus.pending,
      ),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      resolvedAt: json['resolvedAt'] != null 
          ? DateTime.parse(json['resolvedAt']) 
          : null,
      adminResponse: json['adminResponse'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'suggestionId': id,
      'userId': userId,
      'text': text,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'resolvedAt': resolvedAt?.toIso8601String(),
      'adminResponse': adminResponse,
    };
  }

  Suggestion copyWith({
    String? id,
    String? userId,
    String? text,
    SuggestionStatus? status,
    DateTime? createdAt,
    DateTime? resolvedAt,
    String? adminResponse,
  }) {
    return Suggestion(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      text: text ?? this.text,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      resolvedAt: resolvedAt ?? this.resolvedAt,
      adminResponse: adminResponse ?? this.adminResponse,
    );
  }
}

enum SuggestionStatus {
  pending,
  inReview,
  implemented,
  rejected;

  String get displayName {
    switch (this) {
      case SuggestionStatus.pending:
        return 'Pendiente';
      case SuggestionStatus.inReview:
        return 'En revisión';
      case SuggestionStatus.implemented:
        return 'Implementada';
      case SuggestionStatus.rejected:
        return 'Rechazada';
    }
  }
}
