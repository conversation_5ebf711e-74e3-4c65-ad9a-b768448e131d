import 'package:flutter/foundation.dart';
import '../models/models.dart';
import '../services/auth_service.dart';

class AuthProvider with ChangeNotifier {
  final AuthService _authService = AuthService();
  
  User? _currentUser;
  Couple? _currentCouple;
  bool _isLoading = false;
  String? _error;

  User? get currentUser => _currentUser;
  Couple? get currentCouple => _currentCouple;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _currentUser != null;
  bool get hasPartner => _currentCouple != null;

  // Inicializar el provider
  Future<void> initialize() async {
    _setLoading(true);
    try {
      await _authService.initialize();
      _currentUser = _authService.currentUser;
      
      if (_currentUser != null) {
        _currentCouple = await _authService.getCurrentCouple();
      }
      
      _clearError();
    } catch (e) {
      _setError('Error al inicializar: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Iniciar sesión con Google
  Future<bool> signInWithGoogle() async {
    _setLoading(true);
    try {
      _currentUser = await _authService.signInWithGoogle();
      _currentCouple = await _authService.getCurrentCouple();
      _clearError();
      return true;
    } catch (e) {
      _setError('Error al iniciar sesión: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Cerrar sesión
  Future<void> signOut() async {
    _setLoading(true);
    try {
      await _authService.signOut();
      _currentUser = null;
      _currentCouple = null;
      _clearError();
    } catch (e) {
      _setError('Error al cerrar sesión: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Conectar con pareja
  Future<bool> connectWithPartner(String connectionCode) async {
    _setLoading(true);
    try {
      await _authService.connectWithPartner(connectionCode);
      _currentUser = _authService.currentUser;
      _currentCouple = await _authService.getCurrentCouple();
      _clearError();
      return true;
    } catch (e) {
      _setError('Error al conectar con pareja: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Actualizar usuario actual
  Future<void> updateUser(User user) async {
    try {
      await _authService.updateCurrentUser(user);
      _currentUser = user;
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Error al actualizar usuario: $e');
    }
  }

  // Actualizar pareja actual
  void updateCouple(Couple couple) {
    _currentCouple = couple;
    notifyListeners();
  }

  // Métodos privados para manejo de estado
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  // Limpiar error manualmente
  void clearError() {
    _clearError();
  }
}
