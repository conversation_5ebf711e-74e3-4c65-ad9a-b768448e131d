import '../models/models.dart';

class DemoDataProvider {
  static const String demoUserId = 'demo_user_1';
  static const String demoPartnerId = 'demo_user_2';
  static const String demoCoupleId = 'demo_couple_1';

  // Usuario demo
  static User getDemoUser() {
    return User(
      userId: demoUserId,
      email: '<EMAIL>',
      name: '<PERSON>',
      profilePic: '',
      connectionCode: 'DEMO01',
      createdAt: DateTime.now().subtract(const Duration(days: 45)),
      coupleId: demoCoupleId,
    );
  }

  // Pareja demo
  static Couple getDemoCouple() {
    return Couple(
      coupleId: demoCoupleId,
      user1Id: demoUserId,
      user2Id: demoPartnerId,
      treeType: 'roble',
      level: 3,
      experiencePoints: 150,
      createdAt: DateTime.now().subtract(const Duration(days: 45)),
    );
  }

  // Progreso del árbol demo
  static TreeProgress getDemoTreeProgress() {
    return TreeProgress(
      coupleId: demoCoupleId,
      currentStage: TreeStage.plantula,
      lastWatered: DateTime.now().subtract(const Duration(hours: 2)),
      wateringStreak: 12,
      totalWaterings: 28,
    );
  }

  // Historial de riego demo
  static List<WateringRecord> getDemoWateringHistory() {
    final now = DateTime.now();
    return [
      WateringRecord(
        coupleId: demoCoupleId,
        date: now.subtract(const Duration(hours: 2)),
        wateredBy: demoUserId,
        pointsEarned: 10,
        timestamp: now.subtract(const Duration(hours: 2)),
      ),
      WateringRecord(
        coupleId: demoCoupleId,
        date: now.subtract(const Duration(days: 1)),
        wateredBy: demoPartnerId,
        pointsEarned: 10,
        timestamp: now.subtract(const Duration(days: 1)),
      ),
      WateringRecord(
        coupleId: demoCoupleId,
        date: now.subtract(const Duration(days: 2)),
        wateredBy: demoUserId,
        pointsEarned: 10,
        timestamp: now.subtract(const Duration(days: 2)),
      ),
    ];
  }

  // Planes de vida demo
  static List<LifePlan> getDemoLifePlans() {
    final now = DateTime.now();
    return [
      LifePlan(
        id: 'plan_1',
        coupleId: demoCoupleId,
        name: 'Conocernos',
        goals: [
          'Salir en citas regulares',
          'Presentar a amigos y familia',
          'Descubrir intereses comunes',
          'Establecer comunicación abierta'
        ],
        estimatedDate: now.add(const Duration(days: 90)),
        completed: true,
        order: 1,
        additionalComments: 'La etapa inicial donde nos conocimos profundamente',
        createdAt: now.subtract(const Duration(days: 40)),
        completedAt: now.subtract(const Duration(days: 10)),
      ),
      LifePlan(
        id: 'plan_2',
        coupleId: demoCoupleId,
        name: 'Compromiso',
        goals: [
          'Hablar sobre el futuro juntos',
          'Conocer a las familias',
          'Planificar la propuesta',
          'Ahorrar para el anillo'
        ],
        estimatedDate: now.add(const Duration(days: 365)),
        completed: false,
        order: 2,
        additionalComments: 'Decidir formalizar nuestra relación',
        createdAt: now.subtract(const Duration(days: 30)),
      ),
      LifePlan(
        id: 'plan_3',
        coupleId: demoCoupleId,
        name: 'Matrimonio',
        goals: [
          'Planificar la boda',
          'Elegir el lugar y fecha',
          'Organizar luna de miel',
          'Preparar documentos legales'
        ],
        estimatedDate: now.add(const Duration(days: 730)),
        completed: false,
        order: 3,
        additionalComments: 'La celebración de nuestra unión',
        createdAt: now.subtract(const Duration(days: 20)),
      ),
    ];
  }

  // Lugares especiales demo
  static List<SpecialPlace> getDemoSpecialPlaces() {
    final now = DateTime.now();
    return [
      SpecialPlace(
        id: 'place_1',
        coupleId: demoCoupleId,
        name: 'Nuestra primera cita',
        description: 'El café donde nos conocimos y tuvimos nuestra primera conversación de horas. Fue mágico desde el primer momento. Pedimos dos cappuccinos y hablamos hasta que cerraron el lugar.',
        location: PlaceLocation(
          latitude: 19.4326,
          longitude: -99.1332,
          address: 'Café Central, Roma Norte, Ciudad de México',
        ),
        photos: ['demo_photo_1.jpg'],
        createdAt: now.subtract(const Duration(days: 35)),
      ),
      SpecialPlace(
        id: 'place_2',
        coupleId: demoCoupleId,
        name: 'Donde dijimos "Te amo"',
        description: 'El parque donde por primera vez nos dijimos que nos amábamos. Bajo ese árbol de cerezos en flor, con la puesta de sol de fondo. Un momento que nunca olvidaremos.',
        location: PlaceLocation(
          latitude: 19.4284,
          longitude: -99.1276,
          address: 'Parque México, Roma Norte, Ciudad de México',
        ),
        photos: ['demo_photo_2.jpg', 'demo_photo_3.jpg'],
        createdAt: now.subtract(const Duration(days: 20)),
      ),
      SpecialPlace(
        id: 'place_3',
        coupleId: demoCoupleId,
        name: 'Nuestro restaurante favorito',
        description: 'El lugar donde celebramos nuestro primer mes juntos. La comida es deliciosa y el ambiente es perfecto para conversaciones íntimas.',
        location: PlaceLocation(
          latitude: 19.4150,
          longitude: -99.1300,
          address: 'Restaurante Amor, Condesa, Ciudad de México',
        ),
        photos: ['demo_photo_4.jpg'],
        createdAt: now.subtract(const Duration(days: 15)),
      ),
    ];
  }

  // Mensajes de chat demo
  static List<ChatMessage> getDemoChatMessages() {
    final now = DateTime.now();
    return [
      ChatMessage(
        messageId: 'msg_1',
        coupleId: demoCoupleId,
        senderId: demoPartnerId,
        content: '¡Hola mi amor! ¿Cómo va tu día?',
        type: MessageType.text,
        timestamp: now.subtract(const Duration(hours: 3)),
        isRead: true,
      ),
      ChatMessage(
        messageId: 'msg_2',
        coupleId: demoCoupleId,
        senderId: demoUserId,
        content: 'Hola cariño! Todo bien, pensando en ti ❤️',
        type: MessageType.text,
        timestamp: now.subtract(const Duration(hours: 2, minutes: 45)),
        isRead: true,
      ),
      ChatMessage(
        messageId: 'msg_3',
        coupleId: demoCoupleId,
        senderId: demoPartnerId,
        content: '¿Ya regaste nuestro árbol hoy?',
        type: MessageType.text,
        timestamp: now.subtract(const Duration(hours: 2, minutes: 30)),
        isRead: true,
      ),
      ChatMessage(
        messageId: 'msg_4',
        coupleId: demoCoupleId,
        senderId: demoUserId,
        content: '¡Sí! Ya lo hice esta mañana 🌱 ¿Viste cómo está creciendo?',
        type: MessageType.text,
        timestamp: now.subtract(const Duration(hours: 2, minutes: 25)),
        isRead: true,
      ),
      ChatMessage(
        messageId: 'msg_5',
        coupleId: demoCoupleId,
        senderId: demoPartnerId,
        content: 'Me encanta verlo crecer, como nuestro amor 💕',
        type: MessageType.text,
        timestamp: now.subtract(const Duration(hours: 2, minutes: 20)),
        isRead: true,
      ),
      ChatMessage(
        messageId: 'msg_6',
        coupleId: demoCoupleId,
        senderId: demoUserId,
        content: '¿Nos vemos esta noche para cenar?',
        type: MessageType.text,
        timestamp: now.subtract(const Duration(minutes: 30)),
        isRead: false,
      ),
    ];
  }

  // Ubicaciones demo
  static List<UserLocation> getDemoUserLocations() {
    final now = DateTime.now();
    return [
      UserLocation(
        userId: demoUserId,
        latitude: 19.4326,
        longitude: -99.1332,
        timestamp: now.subtract(const Duration(minutes: 2)),
        expiresAt: now.add(const Duration(hours: 1)),
      ),
      UserLocation(
        userId: demoPartnerId,
        latitude: 19.4284,
        longitude: -99.1276,
        timestamp: now.subtract(const Duration(minutes: 5)),
        expiresAt: now.add(const Duration(hours: 1)),
      ),
    ];
  }

  // Sugerencias demo
  static List<Suggestion> getDemoSuggestions() {
    final now = DateTime.now();
    return [
      Suggestion(
        id: 'sugg_1',
        userId: demoUserId,
        text: 'Sería genial poder agregar más tipos de árboles como bambú o bonsái',
        status: SuggestionStatus.implemented,
        createdAt: now.subtract(const Duration(days: 30)),
        resolvedAt: now.subtract(const Duration(days: 5)),
        adminResponse: '¡Excelente idea! Hemos agregado 3 nuevos tipos de árboles en la última actualización.',
      ),
      Suggestion(
        id: 'sugg_2',
        userId: demoUserId,
        text: 'Podrían agregar notificaciones para recordar regar el árbol',
        status: SuggestionStatus.inReview,
        createdAt: now.subtract(const Duration(days: 15)),
      ),
      Suggestion(
        id: 'sugg_3',
        userId: demoUserId,
        text: 'Me gustaría poder exportar nuestros lugares especiales a Google Maps',
        status: SuggestionStatus.pending,
        createdAt: now.subtract(const Duration(days: 7)),
      ),
    ];
  }

  // Pregunta del día demo
  static Question getDemoTodaysQuestion() {
    return Question(
      id: 'q_demo_1',
      questionText: '¿Cuál fue el momento más especial que vivieron juntos esta semana?',
      category: QuestionCategory.daily,
      date: DateTime.now(),
      coupleId: demoCoupleId,
      answers: [
        QuestionAnswer(
          userId: demoUserId,
          text: 'Definitivamente cuando fuimos al parque y vimos la puesta de sol juntos. Fue muy romántico y me sentí muy conectada contigo.',
          timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        ),
      ],
    );
  }
}
