import 'package:flutter/foundation.dart';
import '../models/models.dart';
import '../services/api_service.dart';

class TreeProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  TreeProgress? _treeProgress;
  List<WateringRecord> _wateringHistory = [];
  bool _isLoading = false;
  String? _error;

  TreeProgress? get treeProgress => _treeProgress;
  List<WateringRecord> get wateringHistory => _wateringHistory;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get canWaterToday => _treeProgress?.canWaterToday ?? false;

  // Cargar progreso del árbol
  Future<void> loadTreeProgress(String coupleId) async {
    _setLoading(true);
    try {
      _treeProgress = await _apiService.getTreeProgress(coupleId);
      _clearError();
    } catch (e) {
      _setError('Error al cargar progreso del árbol: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Regar el árbol
  Future<bool> waterTree(String coupleId) async {
    if (!canWaterToday) {
      _setError('Ya regaste el árbol hoy');
      return false;
    }

    _setLoading(true);
    try {
      final wateringRecord = await _apiService.waterTree(coupleId);
      
      // Actualizar progreso local
      if (_treeProgress != null) {
        _treeProgress = _treeProgress!.copyWith(
          lastWatered: DateTime.now(),
          wateringStreak: _treeProgress!.wateringStreak + 1,
          totalWaterings: _treeProgress!.totalWaterings + 1,
        );
        
        // Verificar si debe subir de nivel
        _checkLevelUp();
      }
      
      // Agregar registro al historial
      _wateringHistory.insert(0, wateringRecord);
      
      _clearError();
      return true;
    } catch (e) {
      _setError('Error al regar el árbol: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Cargar historial de riego
  Future<void> loadWateringHistory(String coupleId) async {
    try {
      _wateringHistory = await _apiService.getWateringHistory(coupleId);
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Error al cargar historial: $e');
    }
  }

  // Verificar si el árbol debe subir de nivel
  void _checkLevelUp() {
    if (_treeProgress == null) return;

    final currentStage = _treeProgress!.currentStage;
    final totalWaterings = _treeProgress!.totalWaterings;

    TreeStage? nextStage;
    
    if (totalWaterings >= TreeStage.arbolFlorecido.requiredWaterings && 
        currentStage != TreeStage.arbolFlorecido) {
      nextStage = TreeStage.arbolFlorecido;
    } else if (totalWaterings >= TreeStage.arbolMaduro.requiredWaterings && 
               currentStage.index < TreeStage.arbolMaduro.index) {
      nextStage = TreeStage.arbolMaduro;
    } else if (totalWaterings >= TreeStage.arbolJoven.requiredWaterings && 
               currentStage.index < TreeStage.arbolJoven.index) {
      nextStage = TreeStage.arbolJoven;
    } else if (totalWaterings >= TreeStage.plantula.requiredWaterings && 
               currentStage.index < TreeStage.plantula.index) {
      nextStage = TreeStage.plantula;
    } else if (totalWaterings >= TreeStage.brote.requiredWaterings && 
               currentStage.index < TreeStage.brote.index) {
      nextStage = TreeStage.brote;
    }

    if (nextStage != null) {
      _treeProgress = _treeProgress!.copyWith(currentStage: nextStage);
    }
  }

  // Obtener progreso hacia el siguiente nivel
  double get progressToNextLevel {
    if (_treeProgress == null) return 0.0;

    final currentStage = _treeProgress!.currentStage;
    final totalWaterings = _treeProgress!.totalWaterings;

    if (currentStage == TreeStage.arbolFlorecido) return 1.0;

    final nextStageIndex = currentStage.index + 1;
    if (nextStageIndex >= TreeStage.values.length) return 1.0;

    final nextStage = TreeStage.values[nextStageIndex];
    final currentRequired = currentStage.requiredWaterings;
    final nextRequired = nextStage.requiredWaterings;

    if (nextRequired <= currentRequired) return 1.0;

    final progress = (totalWaterings - currentRequired) / (nextRequired - currentRequired);
    return progress.clamp(0.0, 1.0);
  }

  // Obtener días consecutivos de riego
  int get consecutiveDays {
    if (_wateringHistory.isEmpty) return 0;

    int consecutive = 0;
    DateTime? lastDate;

    for (final record in _wateringHistory) {
      final recordDate = DateTime(record.date.year, record.date.month, record.date.day);
      
      if (lastDate == null) {
        consecutive = 1;
        lastDate = recordDate;
      } else {
        final difference = lastDate.difference(recordDate).inDays;
        if (difference == 1) {
          consecutive++;
          lastDate = recordDate;
        } else {
          break;
        }
      }
    }

    return consecutive;
  }

  // Métodos privados para manejo de estado
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  // Limpiar error manualmente
  void clearError() {
    _clearError();
  }

  // Limpiar datos
  void clear() {
    _treeProgress = null;
    _wateringHistory.clear();
    _clearError();
    notifyListeners();
  }
}
