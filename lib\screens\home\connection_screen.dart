import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../utils/app_colors.dart';

class ConnectionScreen extends StatefulWidget {
  const ConnectionScreen({super.key});

  @override
  State<ConnectionScreen> createState() => _ConnectionScreenState();
}

class _ConnectionScreenState extends State<ConnectionScreen>
    with SingleTickerProviderStateMixin {
  final TextEditingController _connectionCodeController = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  Future<void> _connectWithPartner() async {
    final connectionCode = _connectionCodeController.text.trim().toUpperCase();
    
    if (connectionCode.isEmpty) {
      _showSnackBar('Por favor ingresa un código de conexión', isError: true);
      return;
    }

    if (connectionCode.length != 6) {
      _showSnackBar('El código debe tener 6 caracteres', isError: true);
      return;
    }

    final authProvider = context.read<AuthProvider>();
    final success = await authProvider.connectWithPartner(connectionCode);

    if (success) {
      _showSnackBar('¡Conectado exitosamente con tu pareja!');
    } else {
      _showSnackBar(authProvider.error ?? 'Error al conectar', isError: true);
    }
  }

  void _copyConnectionCode() {
    final authProvider = context.read<AuthProvider>();
    final connectionCode = authProvider.currentUser?.connectionCode ?? '';
    
    Clipboard.setData(ClipboardData(text: connectionCode));
    _showSnackBar('Código copiado al portapapeles');
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? AppColors.error : AppColors.success,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _connectionCodeController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar( 
        title: const Text('Conectar con tu pareja'),
        backgroundColor: AppColors.primary,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.pushNamed(context, '/settings');
            },
            icon: const Icon(Icons.settings),
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: SafeArea(
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      children: [
                        const SizedBox(height: 32),
                        
                        // Ilustración
                        _buildIllustration(),
                        
                        const SizedBox(height: 32),
                        
                        // Título y descripción
                        _buildHeader(),
                        
                        const SizedBox(height: 48),
                        
                        // Tu código de conexión
                        _buildYourConnectionCode(),
                        
                        const SizedBox(height: 32),
                        
                        // Conectar con pareja
                        _buildConnectSection(),
                        
                        const Spacer(),
                        
                        // Información adicional
                        _buildInfoText(),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildIllustration() {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(60),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: const Icon(
        Icons.favorite_border,
        size: 60,
        color: AppColors.primary,
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Text(
          'Conecta con tu pareja',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: 16),
        
        Text(
          'Para comenzar su aventura juntos, uno de ustedes debe compartir su código de conexión con el otro.',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: AppColors.textSecondary,
            height: 1.5,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildYourConnectionCode() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final connectionCode = authProvider.currentUser?.connectionCode ?? '';
        
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                Text(
                  'Tu código de conexión',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                
                const SizedBox(height: 12),
                
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: AppColors.primaryLight,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        connectionCode,
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 2,
                        ),
                      ),
                      const SizedBox(width: 12),
                      IconButton(
                        onPressed: _copyConnectionCode,
                        icon: const Icon(Icons.copy, color: AppColors.primary),
                        tooltip: 'Copiar código',
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildConnectSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Conectar con tu pareja',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const SizedBox(height: 16),
            
            TextField(
              controller: _connectionCodeController,
              decoration: const InputDecoration(
                labelText: 'Código de conexión de tu pareja',
                hintText: 'Ej: ABC123',
                prefixIcon: Icon(Icons.link),
              ),
              textCapitalization: TextCapitalization.characters,
              maxLength: 6,
              onChanged: (value) {
                _connectionCodeController.text = value.toUpperCase();
                _connectionCodeController.selection = TextSelection.fromPosition(
                  TextPosition(offset: _connectionCodeController.text.length),
                );
              },
            ),
            
            const SizedBox(height: 16),
            
            Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                return SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: authProvider.isLoading ? null : _connectWithPartner,
                    child: authProvider.isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('Conectar'),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoText() {
    return Text(
      'Una vez conectados, podrán comenzar a hacer crecer su árbol del amor y disfrutar de todas las funciones de Hopie.',
      style: Theme.of(context).textTheme.bodySmall?.copyWith(
        color: AppColors.textLight,
        height: 1.4,
      ),
      textAlign: TextAlign.center,
    );
  }
}
