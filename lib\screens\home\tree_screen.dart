import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/tree_provider.dart';
import '../../models/models.dart';
import '../../utils/app_colors.dart';
import '../../widgets/tree_widget.dart';
import '../../widgets/daily_question_card.dart';
import '../tree/tree_selection_screen.dart';

class TreeScreen extends StatefulWidget {
  const TreeScreen({super.key});

  @override
  State<TreeScreen> createState() => _TreeScreenState();
}

class _TreeScreenState extends State<TreeScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();

    // Cargar datos después del primer frame para evitar setState durante build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTreeData();
    });
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  Future<void> _loadTreeData() async {
    final authProvider = context.read<AuthProvider>();
    final treeProvider = context.read<TreeProvider>();
    
    if (authProvider.currentCouple != null) {
      await treeProvider.loadTreeProgress(authProvider.currentCouple!.coupleId);
      await treeProvider.loadWateringHistory(authProvider.currentCouple!.coupleId);
    }
  }

  Future<void> _waterTree() async {
    final authProvider = context.read<AuthProvider>();
    final treeProvider = context.read<TreeProvider>();
    
    if (authProvider.currentCouple != null) {
      final success = await treeProvider.waterTree(authProvider.currentCouple!.coupleId);
      
      if (success) {
        _showSuccessDialog();
      } else {
        _showErrorSnackBar(treeProvider.error ?? 'Error al regar el árbol');
      }
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Row(
          children: [
            Icon(Icons.water_drop, color: AppColors.primary),
            SizedBox(width: 8),
            Text('¡Árbol regado!'),
          ],
        ),
        content: const Text(
          'Has regado el árbol exitosamente. ¡Sigue así para hacerlo crecer!',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Continuar'),
          ),
        ],
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  void _navigateToTreeSelection() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const TreeSelectionScreen(),
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Nuestro Árbol'),
        backgroundColor: AppColors.primary,
        actions: [
          IconButton(
            onPressed: _navigateToTreeSelection,
            icon: const Icon(Icons.settings),
            tooltip: 'Configurar árbol',
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.warmGradient,
        ),
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: RefreshIndicator(
                onRefresh: _loadTreeData,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      // Saludo cálido
                      _buildWarmWelcome(),

                      const SizedBox(height: 20),

                      // Información de la pareja con estilo cálido
                      _buildCoupleInfoWarm(),

                      const SizedBox(height: 24),

                      // Árbol principal con fondo cálido
                      _buildTreeSectionWarm(),

                      const SizedBox(height: 24),

                      // Progreso y estadísticas
                      _buildProgressSection(),

                      const SizedBox(height: 24),

                      // Pregunta diaria
                      const DailyQuestionCard(),

                      const SizedBox(height: 24),

                      // Botón de regar con estilo cálido
                      _buildWaterButtonWarm(),

                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildWarmWelcome() {
    final hour = DateTime.now().hour;
    String greeting;
    IconData greetingIcon;

    if (hour < 12) {
      greeting = '¡Buenos días!';
      greetingIcon = Icons.wb_sunny;
    } else if (hour < 18) {
      greeting = '¡Buenas tardes!';
      greetingIcon = Icons.wb_sunny_outlined;
    } else {
      greeting = '¡Buenas noches!';
      greetingIcon = Icons.nightlight_round;
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.softPeach.withValues(alpha: 0.8),
            AppColors.warmBeige.withValues(alpha: 0.6),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.1),
            blurRadius: 15,
            spreadRadius: 2,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(50),
            ),
            child: Icon(
              greetingIcon,
              color: AppColors.primary,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  greeting,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Que tengan un día lleno de amor 💕',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCoupleInfoWarm() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final couple = authProvider.currentCouple;
        if (couple == null) return const SizedBox.shrink();

        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.paleGold.withValues(alpha: 0.7),
                AppColors.tertiary.withValues(alpha: 0.5),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  gradient: RadialGradient(
                    colors: [
                      AppColors.primary,
                      AppColors.primaryLight,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(30),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withValues(alpha: 0.3),
                      blurRadius: 10,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.favorite,
                  color: AppColors.white,
                  size: 30,
                ),
              ),

              const SizedBox(width: 20),

              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Nivel ${couple.level} 🌟',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${couple.experiencePoints} puntos de amor',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCoupleInfo() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final couple = authProvider.currentCouple;
        if (couple == null) return const SizedBox.shrink();

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: const Icon(
                    Icons.favorite,
                    color: AppColors.white,
                    size: 24,
                  ),
                ),
                
                const SizedBox(width: 16),
                
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Nivel ${couple.level}',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      Text(
                        '${couple.experiencePoints} puntos de experiencia',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.primaryLight,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    couple.treeType.toUpperCase(),
                    style: const TextStyle(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTreeSectionWarm() {
    return Consumer<TreeProvider>(
      builder: (context, treeProvider, child) {
        if (treeProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        return Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: RadialGradient(
              center: Alignment.center,
              colors: [
                AppColors.softPeach.withValues(alpha: 0.3),
                AppColors.warmBeige.withValues(alpha: 0.1),
                Colors.transparent,
              ],
              stops: const [0.0, 0.7, 1.0],
            ),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.2),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withValues(alpha: 0.1),
                blurRadius: 20,
                spreadRadius: 5,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            children: [
              // Título del árbol con emoji
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '🌳 ',
                    style: TextStyle(fontSize: 24),
                  ),
                  Text(
                    treeProvider.treeProgress?.currentStage.displayName ?? 'Semilla',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  Text(
                    ' 🌳',
                    style: TextStyle(fontSize: 24),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Descripción con estilo cálido
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: AppColors.paleGold.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Text(
                  treeProvider.treeProgress?.currentStage.description ?? 'El comienzo de su historia',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.textSecondary,
                    fontStyle: FontStyle.italic,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: 24),

              // Widget del árbol con fondo cálido
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: RadialGradient(
                    colors: [
                      AppColors.tertiary.withValues(alpha: 0.4),
                      AppColors.softPeach.withValues(alpha: 0.2),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(120),
                ),
                child: TreeWidget(
                  stage: treeProvider.treeProgress?.currentStage ?? TreeStage.semilla,
                  treeType: context.read<AuthProvider>().currentCouple?.treeType ?? 'roble',
                  size: 220,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTreeSection() {
    return Consumer<TreeProvider>(
      builder: (context, treeProvider, child) {
        if (treeProvider.isLoading) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: Center(
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Text(
                  treeProvider.treeProgress?.currentStage.displayName ?? 'Semilla',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                
                const SizedBox(height: 8),
                
                Text(
                  treeProvider.treeProgress?.currentStage.description ?? 'El comienzo de su historia',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 24),
                
                // Widget del árbol
                TreeWidget(
                  stage: treeProvider.treeProgress?.currentStage ?? TreeStage.semilla,
                  treeType: context.read<AuthProvider>().currentCouple?.treeType ?? 'roble',
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildProgressSection() {
    return Consumer<TreeProvider>(
      builder: (context, treeProvider, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Progreso',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Barra de progreso
                LinearProgressIndicator(
                  value: treeProvider.progressToNextLevel,
                  backgroundColor: AppColors.greyLight,
                  valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
                
                const SizedBox(height: 16),
                
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildStatItem(
                      'Riegos totales',
                      '${treeProvider.treeProgress?.totalWaterings ?? 0}',
                      Icons.water_drop,
                    ),
                    _buildStatItem(
                      'Días consecutivos',
                      '${treeProvider.consecutiveDays}',
                      Icons.local_fire_department,
                    ),
                    _buildStatItem(
                      'Racha actual',
                      '${treeProvider.treeProgress?.wateringStreak ?? 0}',
                      Icons.trending_up,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: AppColors.primary, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildWaterButtonWarm() {
    return Consumer<TreeProvider>(
      builder: (context, treeProvider, child) {
        final canWater = treeProvider.canWaterToday;

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: canWater
                  ? [
                      AppColors.accent,
                      AppColors.secondary,
                    ]
                  : [
                      AppColors.textLight,
                      AppColors.greyLight,
                    ],
            ),
            borderRadius: BorderRadius.circular(25),
            boxShadow: canWater ? [
              BoxShadow(
                color: AppColors.accent.withValues(alpha: 0.4),
                blurRadius: 15,
                spreadRadius: 3,
                offset: const Offset(0, 6),
              ),
            ] : [],
          ),
          child: Container(
            decoration: BoxDecoration(
              color: AppColors.warmBeige.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(22),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: canWater ? _waterTree : null,
                borderRadius: BorderRadius.circular(22),
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 24),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: canWater
                              ? AppColors.accent.withValues(alpha: 0.2)
                              : AppColors.textLight.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(50),
                        ),
                        child: Icon(
                          canWater ? Icons.water_drop : Icons.check_circle,
                          color: canWater ? AppColors.accent : AppColors.textLight,
                          size: 28,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              canWater ? '💧 Regar nuestro árbol' : '✅ Ya regado hoy',
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: canWater ? AppColors.textPrimary : AppColors.textLight,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              canWater
                                  ? 'Nutran su amor con cuidado diario'
                                  : 'Vuelvan mañana para seguir creciendo',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: canWater ? AppColors.textSecondary : AppColors.textLight,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (canWater) ...[
                        const SizedBox(width: 12),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: AppColors.accent.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            '❤️',
                            style: TextStyle(fontSize: 20),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildWaterButton() {
    return Consumer<TreeProvider>(
      builder: (context, treeProvider, child) {
        final canWater = treeProvider.canWaterToday;
        
        return SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton.icon(
            onPressed: canWater && !treeProvider.isLoading ? _waterTree : null,
            icon: treeProvider.isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                    ),
                  )
                : const Icon(Icons.water_drop, color: AppColors.white),
            label: Text(
              canWater
                  ? (treeProvider.isLoading ? 'Regando...' : 'Regar árbol')
                  : 'Ya regaste hoy',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.white,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: canWater ? AppColors.primary : AppColors.grey,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: canWater ? 4 : 0,
            ),
          ),
        );
      },
    );
  }
}
