import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/tree_provider.dart';
import '../../models/models.dart';
import '../../utils/app_colors.dart';
import '../../widgets/tree_widget.dart';
import '../../widgets/daily_question_card.dart';
import '../tree/tree_selection_screen.dart';

class TreeScreen extends StatefulWidget {
  const TreeScreen({super.key});

  @override
  State<TreeScreen> createState() => _TreeScreenState();
}

class _TreeScreenState extends State<TreeScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadTreeData();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  Future<void> _loadTreeData() async {
    final authProvider = context.read<AuthProvider>();
    final treeProvider = context.read<TreeProvider>();
    
    if (authProvider.currentCouple != null) {
      await treeProvider.loadTreeProgress(authProvider.currentCouple!.coupleId);
      await treeProvider.loadWateringHistory(authProvider.currentCouple!.coupleId);
    }
  }

  Future<void> _waterTree() async {
    final authProvider = context.read<AuthProvider>();
    final treeProvider = context.read<TreeProvider>();
    
    if (authProvider.currentCouple != null) {
      final success = await treeProvider.waterTree(authProvider.currentCouple!.coupleId);
      
      if (success) {
        _showSuccessDialog();
      } else {
        _showErrorSnackBar(treeProvider.error ?? 'Error al regar el árbol');
      }
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Row(
          children: [
            Icon(Icons.water_drop, color: AppColors.primary),
            SizedBox(width: 8),
            Text('¡Árbol regado!'),
          ],
        ),
        content: const Text(
          'Has regado el árbol exitosamente. ¡Sigue así para hacerlo crecer!',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Continuar'),
          ),
        ],
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  void _navigateToTreeSelection() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const TreeSelectionScreen(),
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Nuestro Árbol'),
        backgroundColor: AppColors.primary,
        actions: [
          IconButton(
            onPressed: _navigateToTreeSelection,
            icon: const Icon(Icons.settings),
            tooltip: 'Configurar árbol',
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: RefreshIndicator(
                onRefresh: _loadTreeData,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      // Información de la pareja
                      _buildCoupleInfo(),
                      
                      const SizedBox(height: 24),
                      
                      // Árbol principal
                      _buildTreeSection(),
                      
                      const SizedBox(height: 24),
                      
                      // Progreso y estadísticas
                      _buildProgressSection(),
                      
                      const SizedBox(height: 24),
                      
                      // Pregunta diaria
                      const DailyQuestionCard(),
                      
                      const SizedBox(height: 24),
                      
                      // Botón de regar
                      _buildWaterButton(),
                      
                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildCoupleInfo() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final couple = authProvider.currentCouple;
        if (couple == null) return const SizedBox.shrink();

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: const Icon(
                    Icons.favorite,
                    color: AppColors.white,
                    size: 24,
                  ),
                ),
                
                const SizedBox(width: 16),
                
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Nivel ${couple.level}',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      Text(
                        '${couple.experiencePoints} puntos de experiencia',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.primaryLight,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    couple.treeType.toUpperCase(),
                    style: const TextStyle(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTreeSection() {
    return Consumer<TreeProvider>(
      builder: (context, treeProvider, child) {
        if (treeProvider.isLoading) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: Center(
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Text(
                  treeProvider.treeProgress?.currentStage.displayName ?? 'Semilla',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                
                const SizedBox(height: 8),
                
                Text(
                  treeProvider.treeProgress?.currentStage.description ?? 'El comienzo de su historia',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 24),
                
                // Widget del árbol
                TreeWidget(
                  stage: treeProvider.treeProgress?.currentStage ?? TreeStage.semilla,
                  treeType: context.read<AuthProvider>().currentCouple?.treeType ?? 'roble',
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildProgressSection() {
    return Consumer<TreeProvider>(
      builder: (context, treeProvider, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Progreso',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Barra de progreso
                LinearProgressIndicator(
                  value: treeProvider.progressToNextLevel,
                  backgroundColor: AppColors.greyLight,
                  valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
                
                const SizedBox(height: 16),
                
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildStatItem(
                      'Riegos totales',
                      '${treeProvider.treeProgress?.totalWaterings ?? 0}',
                      Icons.water_drop,
                    ),
                    _buildStatItem(
                      'Días consecutivos',
                      '${treeProvider.consecutiveDays}',
                      Icons.local_fire_department,
                    ),
                    _buildStatItem(
                      'Racha actual',
                      '${treeProvider.treeProgress?.wateringStreak ?? 0}',
                      Icons.trending_up,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: AppColors.primary, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildWaterButton() {
    return Consumer<TreeProvider>(
      builder: (context, treeProvider, child) {
        final canWater = treeProvider.canWaterToday;
        
        return SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton.icon(
            onPressed: canWater && !treeProvider.isLoading ? _waterTree : null,
            icon: treeProvider.isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                    ),
                  )
                : const Icon(Icons.water_drop, color: AppColors.white),
            label: Text(
              canWater
                  ? (treeProvider.isLoading ? 'Regando...' : 'Regar árbol')
                  : 'Ya regaste hoy',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.white,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: canWater ? AppColors.primary : AppColors.grey,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: canWater ? 4 : 0,
            ),
          ),
        );
      },
    );
  }
}
