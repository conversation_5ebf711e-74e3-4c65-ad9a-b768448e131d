import 'package:flutter/material.dart';
import '../../models/models.dart';
import '../../utils/app_colors.dart';

class AddLifePlanScreen extends StatefulWidget {
  const AddLifePlanScreen({super.key});

  @override
  State<AddLifePlanScreen> createState() => _AddLifePlanScreenState();
}

class _AddLifePlanScreenState extends State<AddLifePlanScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _commentsController = TextEditingController();
  final List<TextEditingController> _goalControllers = [TextEditingController()];
  DateTime? _selectedDate;
  LifePlanTemplate? _selectedTemplate;
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _commentsController.dispose();
    for (final controller in _goalControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Nueva Etapa'),
        backgroundColor: AppColors.primary,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _savePlan,
            child: const Text(
              'Guardar',
              style: TextStyle(color: AppColors.white, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: Form(
          key: _formKey,
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Plantillas predefinidas
              _buildTemplateSection(),
              
              const SizedBox(height: 24),
              
              // Información básica
              _buildBasicInfoSection(),
              
              const SizedBox(height: 24),
              
              // Metas
              _buildGoalsSection(),
              
              const SizedBox(height: 24),
              
              // Fecha estimada
              _buildDateSection(),
              
              const SizedBox(height: 24),
              
              // Comentarios adicionales
              _buildCommentsSection(),
              
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTemplateSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Plantillas sugeridas',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Puedes usar una plantilla como punto de partida',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: LifePlanTemplate.getDefaultTemplates().map((template) {
                final isSelected = _selectedTemplate == template;
                return FilterChip(
                  label: Text(template.name),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        _selectedTemplate = template;
                        _applyTemplate(template);
                      } else {
                        _selectedTemplate = null;
                      }
                    });
                  },
                  selectedColor: AppColors.primary.withOpacity(0.2),
                  checkmarkColor: AppColors.primary,
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Información básica',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Nombre de la etapa',
                hintText: 'Ej: Matrimonio, Primer hogar, etc.',
                prefixIcon: Icon(Icons.label),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Por favor ingresa un nombre';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoalsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Metas y objetivos',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: _addGoal,
                  icon: const Icon(Icons.add, color: AppColors.primary),
                  tooltip: 'Agregar meta',
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._goalControllers.asMap().entries.map((entry) {
              final index = entry.key;
              final controller = entry.value;
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: controller,
                        decoration: InputDecoration(
                          labelText: 'Meta ${index + 1}',
                          hintText: 'Describe una meta específica',
                          prefixIcon: const Icon(Icons.flag),
                        ),
                        validator: (value) {
                          if (index == 0 && (value == null || value.trim().isEmpty)) {
                            return 'Debe tener al menos una meta';
                          }
                          return null;
                        },
                      ),
                    ),
                    if (_goalControllers.length > 1)
                      IconButton(
                        onPressed: () => _removeGoal(index),
                        icon: const Icon(Icons.remove_circle, color: AppColors.error),
                      ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildDateSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Fecha estimada',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            InkWell(
              onTap: _selectDate,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.calendar_today, color: AppColors.primary),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _selectedDate != null
                            ? _formatDate(_selectedDate!)
                            : 'Seleccionar fecha (opcional)',
                        style: TextStyle(
                          color: _selectedDate != null
                              ? AppColors.textPrimary
                              : AppColors.textSecondary,
                        ),
                      ),
                    ),
                    if (_selectedDate != null)
                      IconButton(
                        onPressed: () {
                          setState(() {
                            _selectedDate = null;
                          });
                        },
                        icon: const Icon(Icons.clear, color: AppColors.error),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommentsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Comentarios adicionales',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _commentsController,
              maxLines: 3,
              decoration: const InputDecoration(
                labelText: 'Comentarios (opcional)',
                hintText: 'Agrega cualquier información adicional sobre esta etapa',
                prefixIcon: Icon(Icons.comment),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _applyTemplate(LifePlanTemplate template) {
    _nameController.text = template.name;
    _commentsController.text = template.description;
    
    // Limpiar controladores existentes
    for (final controller in _goalControllers) {
      controller.dispose();
    }
    
    // Crear nuevos controladores con las metas de la plantilla
    _goalControllers.clear();
    for (final goal in template.defaultGoals) {
      final controller = TextEditingController(text: goal);
      _goalControllers.add(controller);
    }
    
    setState(() {});
  }

  void _addGoal() {
    setState(() {
      _goalControllers.add(TextEditingController());
    });
  }

  void _removeGoal(int index) {
    if (_goalControllers.length > 1) {
      setState(() {
        _goalControllers[index].dispose();
        _goalControllers.removeAt(index);
      });
    }
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now().add(const Duration(days: 365)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 10)),
    );
    
    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  String _formatDate(DateTime date) {
    final months = [
      'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
      'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
    ];
    return '${date.day} de ${months[date.month - 1]} de ${date.year}';
  }

  Future<void> _savePlan() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Recopilar metas no vacías
      final goals = _goalControllers
          .map((controller) => controller.text.trim())
          .where((goal) => goal.isNotEmpty)
          .toList();

      // Aquí implementarías la lógica para guardar en la API
      await Future.delayed(const Duration(seconds: 1));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Etapa creada exitosamente'),
            backgroundColor: AppColors.success,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error al crear la etapa'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
