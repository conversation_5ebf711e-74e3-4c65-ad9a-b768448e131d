import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/demo_data_provider.dart';
import '../../models/models.dart';
import '../../utils/app_colors.dart';
import 'add_life_plan_screen.dart';
import 'edit_life_plan_screen.dart';

class LifePlanScreen extends StatefulWidget {
  const LifePlanScreen({super.key});

  @override
  State<LifePlanScreen> createState() => _LifePlanScreenState();
}

class _LifePlanScreenState extends State<LifePlanScreen> {
  List<LifePlan> _lifePlans = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadLifePlans();
  }

  Future<void> _loadLifePlans() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Simular datos de ejemplo
      _lifePlans = _getExampleLifePlans();
    } catch (e) {
      // Manejar error
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<LifePlan> _getExampleLifePlans() {
    final authProvider = context.read<AuthProvider>();

    // Si es modo demo, usar datos demo
    if (authProvider.currentCouple?.coupleId == DemoDataProvider.demoCoupleId) {
      return DemoDataProvider.getDemoLifePlans();
    }

    // Datos de ejemplo para desarrollo
    final templates = LifePlanTemplate.getDefaultTemplates();
    return templates.asMap().entries.map((entry) {
      final index = entry.key;
      final template = entry.value;
      return LifePlan(
        id: 'plan_$index',
        coupleId: 'couple_1',
        name: template.name,
        goals: template.defaultGoals,
        estimatedDate: DateTime.now().add(Duration(days: (index + 1) * 365)),
        completed: index == 0, // Solo el primero completado
        order: index + 1,
        additionalComments: template.description,
        createdAt: DateTime.now(),
        completedAt: index == 0 ? DateTime.now().subtract(const Duration(days: 30)) : null,
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Nuestro Plan de Vida'),
        backgroundColor: AppColors.primary,
        actions: [
          IconButton(
            onPressed: _addNewPlan,
            icon: const Icon(Icons.add),
            tooltip: 'Agregar etapa',
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : RefreshIndicator(
                onRefresh: _loadLifePlans,
                child: _lifePlans.isEmpty
                    ? _buildEmptyState()
                    : _buildLifePlansList(),
              ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.timeline,
              size: 80,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 24),
            Text(
              'Aún no tienen un plan de vida',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'Creen juntos las etapas de su futuro y trabajen hacia sus metas compartidas',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: _addNewPlan,
              icon: const Icon(Icons.add, color: AppColors.white),
              label: const Text(
                'Crear primer plan',
                style: TextStyle(color: AppColors.white),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLifePlansList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _lifePlans.length,
      itemBuilder: (context, index) {
        final plan = _lifePlans[index];
        return _buildLifePlanCard(plan, index);
      },
    );
  }

  Widget _buildLifePlanCard(LifePlan plan, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: InkWell(
          onTap: () => _editPlan(plan),
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header con número y estado
                Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: plan.completed ? AppColors.success : AppColors.primary,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Center(
                        child: plan.completed
                            ? const Icon(Icons.check, color: AppColors.white, size: 20)
                            : Text(
                                '${index + 1}',
                                style: const TextStyle(
                                  color: AppColors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ),
                    
                    const SizedBox(width: 12),
                    
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            plan.name,
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.textPrimary,
                            ),
                          ),
                          if (plan.estimatedDate != null)
                            Text(
                              'Meta: ${_formatDate(plan.estimatedDate!)}',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            ),
                        ],
                      ),
                    ),
                    
                    if (plan.completed)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppColors.success.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'Completado',
                          style: TextStyle(
                            color: AppColors.success,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Metas
                Text(
                  'Metas:',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 8),
                ...plan.goals.take(3).map((goal) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(
                        Icons.check_circle_outline,
                        size: 16,
                        color: AppColors.primary,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          goal,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
                
                if (plan.goals.length > 3)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      '+ ${plan.goals.length - 3} metas más',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                
                // Comentarios adicionales
                if (plan.additionalComments != null && plan.additionalComments!.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColors.primaryLight.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      plan.additionalComments!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final months = [
      'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
      'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
    ];
    return '${months[date.month - 1]} ${date.year}';
  }

  void _addNewPlan() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AddLifePlanScreen(),
      ),
    ).then((_) => _loadLifePlans());
  }

  void _editPlan(LifePlan plan) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => EditLifePlanScreen(lifePlan: plan),
      ),
    ).then((_) => _loadLifePlans());
  }
}
