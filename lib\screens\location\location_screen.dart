import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/models.dart';
import '../../utils/app_colors.dart';
import '../../widgets/scrollable_screen.dart';

class LocationScreen extends StatefulWidget {
  const LocationScreen({super.key});

  @override
  State<LocationScreen> createState() => _LocationScreenState();
}

class _LocationScreenState extends State<LocationScreen> {
  List<UserLocation> _locations = [];
  bool _isLoading = false;
  bool _sharingLocation = true;

  @override
  void initState() {
    super.initState();
    _loadLocations();
  }

  Future<void> _loadLocations() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Simular datos de ejemplo
      _locations = _getExampleLocations();
    } catch (e) {
      // Manejar error
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<UserLocation> _getExampleLocations() {
    final currentUser = context.read<AuthProvider>().currentUser;
    return [
      UserLocation(
        userId: currentUser?.userId ?? 'current_user',
        latitude: 19.4326,
        longitude: -99.1332,
        timestamp: DateTime.now(),
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
      ),
      UserLocation(
        userId: 'partner_id',
        latitude: 19.4284,
        longitude: -99.1276,
        timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ubicación en Tiempo Real'),
        backgroundColor: AppColors.primary,
        actions: [
          IconButton(
            onPressed: _showLocationSettings,
            icon: const Icon(Icons.settings),
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : ScrollableScreen(
                padding: EdgeInsets.zero,
                child: Column(
                  children: [
                    // Estado de compartir ubicación
                    _buildLocationSharingCard(),

                    // Mapa grande
                    _buildLargeMap(),

                    // Lista de ubicaciones
                    _buildLocationsList(),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildLocationSharingCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(
                _sharingLocation ? Icons.location_on : Icons.location_off,
                color: _sharingLocation ? AppColors.success : AppColors.error,
                size: 28,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _sharingLocation 
                          ? 'Compartiendo ubicación'
                          : 'Ubicación desactivada',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: _sharingLocation ? AppColors.success : AppColors.error,
                      ),
                    ),
                    Text(
                      _sharingLocation
                          ? 'Tu pareja puede ver tu ubicación actual'
                          : 'Activa para compartir tu ubicación',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Switch(
                value: _sharingLocation,
                onChanged: (value) {
                  setState(() {
                    _sharingLocation = value;
                  });
                  _toggleLocationSharing(value);
                },
                activeColor: AppColors.success,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLargeMap() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6, // 60% de la pantalla
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withValues(alpha: 0.1),
            AppColors.secondary.withValues(alpha: 0.1),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.charcoal.withValues(alpha: 0.1),
            blurRadius: 15,
            spreadRadius: 5,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Stack(
          children: [
            // Fondo del mapa con patrón
            Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  center: Alignment.center,
                  colors: [
                    AppColors.tertiary.withValues(alpha: 0.3),
                    AppColors.quaternary.withValues(alpha: 0.1),
                  ],
                ),
              ),
            ),

            // Placeholder del mapa grande
            const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.public,
                    size: 80,
                    color: AppColors.primary,
                  ),
                  SizedBox(height: 20),
                  Text(
                    'Mapa en Tiempo Real',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  SizedBox(height: 12),
                  Text(
                    'Aquí se mostraría Google Maps\ncon las ubicaciones de ambos en tiempo real',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),

            // Botones de acción flotantes
            Positioned(
              top: 20,
              right: 20,
              child: Column(
                children: [
                  _buildMapActionButton(
                    icon: Icons.my_location,
                    onPressed: _showMyLocation,
                    tooltip: 'Mi ubicación',
                  ),
                  const SizedBox(height: 12),
                  _buildMapActionButton(
                    icon: Icons.center_focus_strong,
                    onPressed: _centerOnBoth,
                    tooltip: 'Centrar en ambos',
                  ),
                  const SizedBox(height: 12),
                  _buildMapActionButton(
                    icon: Icons.layers,
                    onPressed: _changeMapType,
                    tooltip: 'Tipo de mapa',
                  ),
                ],
              ),
            ),

            // Marcadores simulados
            if (_locations.isNotEmpty) ...[
              Positioned(
                left: 80,
                top: 120,
                child: _buildMapMarker(
                  isCurrentUser: true,
                  label: 'Tú',
                ),
              ),
              Positioned(
                right: 100,
                bottom: 140,
                child: _buildMapMarker(
                  isCurrentUser: false,
                  label: 'Tu pareja',
                ),
              ),
            ],

            // Información de distancia
            Positioned(
              bottom: 20,
              left: 20,
              child: _buildDistanceInfo(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMapActionButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.charcoal.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Icon(
              icon,
              color: AppColors.primary,
              size: 24,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDistanceInfo() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.charcoal.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.straighten,
            color: AppColors.primary,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            '2.3 km de distancia',
            style: TextStyle(
              color: AppColors.textPrimary,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMapMarker({required bool isCurrentUser, required String label}) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
        ),
        const SizedBox(height: 4),
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: isCurrentUser ? AppColors.primary : AppColors.secondary,
            shape: BoxShape.circle,
            border: Border.all(color: AppColors.white, width: 3),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 6,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: const Icon(
            Icons.person,
            color: AppColors.white,
            size: 20,
          ),
        ),
      ],
    );
  }

  Widget _buildLocationsList() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Ubicaciones actuales',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              ..._locations.map((location) => _buildLocationItem(location)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLocationItem(UserLocation location) {
    final currentUser = context.read<AuthProvider>().currentUser;
    final isCurrentUser = location.userId == currentUser?.userId;
    final timeDiff = DateTime.now().difference(location.timestamp);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isCurrentUser
            ? AppColors.primary.withValues(alpha: 0.1)
            : AppColors.secondary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: isCurrentUser ? AppColors.primary : AppColors.secondary,
            child: const Icon(
              Icons.person,
              color: AppColors.white,
              size: 20,
            ),
          ),
          
          const SizedBox(width: 12),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isCurrentUser ? 'Tu ubicación' : 'Tu pareja',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'Lat: ${location.latitude.toStringAsFixed(4)}, '
                  'Lng: ${location.longitude.toStringAsFixed(4)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'Actualizado hace ${_formatTimeDifference(timeDiff)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          
          Icon(
            Icons.location_on,
            color: isCurrentUser ? AppColors.primary : AppColors.secondary,
          ),
        ],
      ),
    );
  }

  String _formatTimeDifference(Duration difference) {
    if (difference.inMinutes < 1) {
      return 'menos de 1 minuto';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} minuto${difference.inMinutes != 1 ? 's' : ''}';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hora${difference.inHours != 1 ? 's' : ''}';
    } else {
      return '${difference.inDays} día${difference.inDays != 1 ? 's' : ''}';
    }
  }

  void _toggleLocationSharing(bool enabled) {
    if (enabled) {
      // Aquí implementarías la lógica para activar el compartir ubicación
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Ubicación activada'),
          backgroundColor: AppColors.success,
        ),
      );
    } else {
      // Aquí implementarías la lógica para desactivar el compartir ubicación
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Ubicación desactivada'),
          backgroundColor: AppColors.warning,
        ),
      );
    }
  }

  void _showLocationSettings() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Configuración de ubicación',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.refresh, color: AppColors.primary),
              title: const Text('Actualizar ubicación'),
              subtitle: const Text('Forzar actualización manual'),
              onTap: () {
                Navigator.pop(context);
                _loadLocations();
              },
            ),
            ListTile(
              leading: const Icon(Icons.history, color: AppColors.primary),
              title: const Text('Historial de ubicaciones'),
              subtitle: const Text('Ver ubicaciones anteriores'),
              onTap: () {
                Navigator.pop(context);
                // Implementar historial
              },
            ),
            ListTile(
              leading: const Icon(Icons.privacy_tip, color: AppColors.primary),
              title: const Text('Privacidad'),
              subtitle: const Text('Configurar permisos de ubicación'),
              onTap: () {
                Navigator.pop(context);
                // Implementar configuración de privacidad
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showMyLocation() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Centrando en tu ubicación...'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _centerOnBoth() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Centrando en ambas ubicaciones...'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _changeMapType() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Cambiando tipo de mapa...'),
        backgroundColor: AppColors.info,
      ),
    );
  }
}
