import 'package:flutter/material.dart';
import '../../models/models.dart';
import '../../utils/app_colors.dart';
import 'place_detail_screen.dart';

class PlacesMapScreen extends StatefulWidget {
  final List<SpecialPlace> places;

  const PlacesMapScreen({
    super.key,
    required this.places,
  });

  @override
  State<PlacesMapScreen> createState() => _PlacesMapScreenState();
}

class _PlacesMapScreenState extends State<PlacesMapScreen> {
  SpecialPlace? _selectedPlace;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mapa de Lugares'),
        backgroundColor: AppColors.primary,
        actions: [
          IconButton(
            onPressed: _showMapOptions,
            icon: const Icon(Icons.layers),
          ),
        ],
      ),
      body: Stack(
        children: [
          // Mapa placeholder
          _buildMapPlaceholder(),
          
          // Lista de lugares en la parte inferior
          if (widget.places.isNotEmpty)
            _buildPlacesList(),
        ],
      ),
    );
  }

  Widget _buildMapPlaceholder() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withOpacity(0.1),
            AppColors.secondary.withOpacity(0.1),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Fondo del mapa
          const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.map,
                  size: 80,
                  color: AppColors.primary,
                ),
                SizedBox(height: 16),
                Text(
                  'Mapa interactivo',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Aquí se mostraría Google Maps\ncon todos los lugares especiales',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          
          // Marcadores simulados
          ...widget.places.asMap().entries.map((entry) {
            final index = entry.key;
            final place = entry.value;
            return _buildMapMarker(place, index);
          }),
        ],
      ),
    );
  }

  Widget _buildMapMarker(SpecialPlace place, int index) {
    final isSelected = _selectedPlace?.id == place.id;
    
    // Posiciones simuladas para los marcadores
    final positions = [
      const Offset(150, 200),
      const Offset(250, 300),
      const Offset(100, 400),
      const Offset(300, 250),
      const Offset(200, 150),
    ];
    
    final position = positions[index % positions.length];
    
    return Positioned(
      left: position.dx,
      top: position.dy,
      child: GestureDetector(
        onTap: () => _selectPlace(place),
        child: Column(
          children: [
            // Etiqueta del lugar
            if (isSelected)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  place.name,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
            
            if (isSelected) const SizedBox(height: 4),
            
            // Marcador
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              width: isSelected ? 50 : 40,
              height: isSelected ? 50 : 40,
              decoration: BoxDecoration(
                color: isSelected ? AppColors.accent : AppColors.primary,
                shape: BoxShape.circle,
                border: Border.all(
                  color: AppColors.white,
                  width: isSelected ? 4 : 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: isSelected ? 8 : 6,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Icon(
                Icons.place,
                color: AppColors.white,
                size: isSelected ? 25 : 20,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlacesList() {
    return DraggableScrollableSheet(
      initialChildSize: 0.3,
      minChildSize: 0.1,
      maxChildSize: 0.8,
      builder: (context, scrollController) {
        return Container(
          decoration: const BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            boxShadow: [
              BoxShadow(
                color: Colors.black26,
                blurRadius: 10,
                offset: Offset(0, -2),
              ),
            ],
          ),
          child: Column(
            children: [
              // Handle
              Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: AppColors.grey,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              
              // Header
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    Text(
                      'Lugares especiales (${widget.places.length})',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    if (_selectedPlace != null)
                      TextButton(
                        onPressed: _clearSelection,
                        child: const Text('Limpiar'),
                      ),
                  ],
                ),
              ),
              
              // Lista
              Expanded(
                child: ListView.builder(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: widget.places.length,
                  itemBuilder: (context, index) {
                    final place = widget.places[index];
                    return _buildPlaceListItem(place);
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPlaceListItem(SpecialPlace place) {
    final isSelected = _selectedPlace?.id == place.id;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: isSelected ? 8 : 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: isSelected ? AppColors.primary : Colors.transparent,
            width: 2,
          ),
        ),
        child: InkWell(
          onTap: () => _selectPlace(place),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                // Icono
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? AppColors.primary 
                        : AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.place,
                    color: isSelected ? AppColors.white : AppColors.primary,
                    size: 20,
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // Información
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        place.name,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: isSelected ? AppColors.primary : AppColors.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        place.location.address,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                
                // Botón de detalles
                IconButton(
                  onPressed: () => _viewPlaceDetail(place),
                  icon: const Icon(
                    Icons.info_outline,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _selectPlace(SpecialPlace place) {
    setState(() {
      _selectedPlace = _selectedPlace?.id == place.id ? null : place;
    });
  }

  void _clearSelection() {
    setState(() {
      _selectedPlace = null;
    });
  }

  void _viewPlaceDetail(SpecialPlace place) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PlaceDetailScreen(place: place),
      ),
    );
  }

  void _showMapOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Opciones del mapa',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.map, color: AppColors.primary),
              title: const Text('Tipo de mapa'),
              subtitle: const Text('Cambiar entre satélite, terreno, etc.'),
              onTap: () {
                Navigator.pop(context);
                _changeMapType();
              },
            ),
            ListTile(
              leading: const Icon(Icons.center_focus_strong, color: AppColors.primary),
              title: const Text('Centrar en lugares'),
              subtitle: const Text('Ajustar vista para mostrar todos los lugares'),
              onTap: () {
                Navigator.pop(context);
                _centerOnPlaces();
              },
            ),
            ListTile(
              leading: const Icon(Icons.my_location, color: AppColors.primary),
              title: const Text('Mi ubicación'),
              subtitle: const Text('Mostrar mi ubicación actual'),
              onTap: () {
                Navigator.pop(context);
                _showMyLocation();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _changeMapType() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Función próximamente'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _centerOnPlaces() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Centrando en todos los lugares'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _showMyLocation() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Mostrando ubicación actual'),
        backgroundColor: AppColors.info,
      ),
    );
  }
}
