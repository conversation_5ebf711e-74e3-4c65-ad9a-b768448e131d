import 'package:flutter/material.dart';
import '../../models/models.dart';
import '../../utils/app_colors.dart';
import 'add_place_screen.dart';
import 'place_detail_screen.dart';
import 'places_map_screen.dart';

class PlacesScreen extends StatefulWidget {
  const PlacesScreen({super.key});

  @override
  State<PlacesScreen> createState() => _PlacesScreenState();
}

class _PlacesScreenState extends State<PlacesScreen> {
  List<SpecialPlace> _places = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadPlaces();
  }

  Future<void> _loadPlaces() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Simular datos de ejemplo
      _places = _getExamplePlaces();
    } catch (e) {
      // Manejar error
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<SpecialPlace> _getExamplePlaces() {
    return [
      SpecialPlace(
        id: 'place_1',
        coupleId: 'couple_1',
        name: 'Nuestra primera cita',
        description: 'El café donde nos conocimos y tuvimos nuestra primera conversación de horas. Fue mágico desde el primer momento.',
        location: PlaceLocation(
          latitude: 19.4326,
          longitude: -99.1332,
          address: 'Café Central, Ciudad de México',
        ),
        photos: ['https://example.com/photo1.jpg'],
        createdAt: DateTime.now().subtract(const Duration(days: 365)),
      ),
      SpecialPlace(
        id: 'place_2',
        coupleId: 'couple_1',
        name: 'Donde dijimos "Te amo"',
        description: 'El parque donde por primera vez nos dijimos que nos amábamos. Bajo ese árbol de cerezos en flor.',
        location: PlaceLocation(
          latitude: 19.4284,
          longitude: -99.1276,
          address: 'Parque México, Roma Norte',
        ),
        photos: ['https://example.com/photo2.jpg', 'https://example.com/photo3.jpg'],
        createdAt: DateTime.now().subtract(const Duration(days: 200)),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Lugares Especiales'),
        backgroundColor: AppColors.primary,
        actions: [
          IconButton(
            onPressed: _showMap,
            icon: const Icon(Icons.map),
            tooltip: 'Ver en mapa',
          ),
          IconButton(
            onPressed: _addNewPlace,
            icon: const Icon(Icons.add),
            tooltip: 'Agregar lugar',
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : RefreshIndicator(
                onRefresh: _loadPlaces,
                child: _places.isEmpty
                    ? _buildEmptyState()
                    : _buildPlacesList(),
              ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.place,
              size: 80,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 24),
            Text(
              'Aún no tienen lugares especiales',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'Comiencen a documentar los lugares que son importantes en su relación',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: _addNewPlace,
              icon: const Icon(Icons.add, color: AppColors.white),
              label: const Text(
                'Agregar primer lugar',
                style: TextStyle(color: AppColors.white),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlacesList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _places.length,
      itemBuilder: (context, index) {
        final place = _places[index];
        return _buildPlaceCard(place);
      },
    );
  }

  Widget _buildPlaceCard(SpecialPlace place) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: InkWell(
          onTap: () => _viewPlaceDetail(place),
          borderRadius: BorderRadius.circular(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Imagen principal (placeholder)
              Container(
                height: 200,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.primary.withOpacity(0.7),
                      AppColors.secondary.withOpacity(0.7),
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    // Placeholder para imagen
                    const Center(
                      child: Icon(
                        Icons.photo_camera,
                        size: 48,
                        color: AppColors.white,
                      ),
                    ),
                    
                    // Overlay con información
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Colors.black.withOpacity(0.7),
                            ],
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              place.name,
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                color: AppColors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                const Icon(
                                  Icons.location_on,
                                  size: 16,
                                  color: AppColors.white,
                                ),
                                const SizedBox(width: 4),
                                Expanded(
                                  child: Text(
                                    place.location.address,
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: AppColors.white,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Contenido
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      place.description,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.textSecondary,
                        height: 1.4,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 12),
                    
                    // Información adicional
                    Row(
                      children: [
                        if (place.photos.isNotEmpty) ...[
                          const Icon(
                            Icons.photo_library,
                            size: 16,
                            color: AppColors.primary,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${place.photos.length} foto${place.photos.length != 1 ? 's' : ''}',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.primary,
                            ),
                          ),
                          const SizedBox(width: 16),
                        ],
                        
                        const Icon(
                          Icons.calendar_today,
                          size: 16,
                          color: AppColors.textSecondary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _formatDate(place.createdAt),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                        
                        const Spacer(),
                        
                        const Icon(
                          Icons.arrow_forward_ios,
                          size: 16,
                          color: AppColors.textSecondary,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final months = [
      'Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun',
      'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  void _addNewPlace() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const AddPlaceScreen(),
      ),
    ).then((_) => _loadPlaces());
  }

  void _viewPlaceDetail(SpecialPlace place) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PlaceDetailScreen(place: place),
      ),
    );
  }

  void _showMap() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PlacesMapScreen(places: _places),
      ),
    );
  }
}
