import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../utils/app_colors.dart';
import 'suggestions_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _notificationsEnabled = true;
  bool _locationSharingEnabled = true;
  bool _dailyRemindersEnabled = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Configuración'),
        backgroundColor: AppColors.primary,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Perfil de usuario
            _buildUserProfileSection(),
            
            const SizedBox(height: 24),
            
            // Información de la pareja
            _buildCoupleInfoSection(),
            
            const SizedBox(height: 24),
            
            // Configuraciones
            _buildSettingsSection(),
            
            const SizedBox(height: 24),
            
            // Sugerencias y soporte
            _buildSupportSection(),
            
            const SizedBox(height: 24),
            
            // Acciones de cuenta
            _buildAccountActionsSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildUserProfileSection() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.currentUser;
        
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: AppColors.primary,
                  backgroundImage: user?.profilePic.isNotEmpty == true
                      ? NetworkImage(user!.profilePic)
                      : null,
                  child: user?.profilePic.isEmpty != false
                      ? const Icon(
                          Icons.person,
                          size: 30,
                          color: AppColors.white,
                        )
                      : null,
                ),
                
                const SizedBox(width: 16),
                
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user?.name ?? 'Usuario',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        user?.email ?? '',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppColors.primaryLight,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'ID: ${user?.connectionCode ?? ''}',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: AppColors.textPrimary,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                IconButton(
                  onPressed: _editProfile,
                  icon: const Icon(Icons.edit, color: AppColors.primary),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCoupleInfoSection() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final couple = authProvider.currentCouple;
        
        if (couple == null) {
          return const SizedBox.shrink();
        }
        
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Información de la pareja',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                Row(
                  children: [
                    const Icon(Icons.favorite, color: AppColors.primary),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Conectados desde',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                          Text(
                            _formatDate(couple.createdAt),
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                Row(
                  children: [
                    const Icon(Icons.park, color: AppColors.primary),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Árbol seleccionado',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                          Text(
                            couple.treeType.toUpperCase(),
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                Row(
                  children: [
                    const Icon(Icons.trending_up, color: AppColors.primary),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Nivel actual',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                          Text(
                            'Nivel ${couple.level} (${couple.experiencePoints} XP)',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSettingsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Configuraciones',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            _buildSettingItem(
              icon: Icons.notifications,
              title: 'Notificaciones',
              subtitle: 'Recibir notificaciones de la app',
              value: _notificationsEnabled,
              onChanged: (value) {
                setState(() {
                  _notificationsEnabled = value;
                });
              },
            ),
            
            _buildSettingItem(
              icon: Icons.location_on,
              title: 'Compartir ubicación',
              subtitle: 'Permitir que tu pareja vea tu ubicación',
              value: _locationSharingEnabled,
              onChanged: (value) {
                setState(() {
                  _locationSharingEnabled = value;
                });
              },
            ),
            
            _buildSettingItem(
              icon: Icons.alarm,
              title: 'Recordatorios diarios',
              subtitle: 'Recordar regar el árbol y responder preguntas',
              value: _dailyRemindersEnabled,
              onChanged: (value) {
                setState(() {
                  _dailyRemindersEnabled = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Icon(icon, color: AppColors.primary),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildSupportSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Soporte y sugerencias',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            ListTile(
              leading: const Icon(Icons.lightbulb, color: AppColors.primary),
              title: const Text('Enviar sugerencia'),
              subtitle: const Text('Ayúdanos a mejorar la app'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _openSuggestions,
            ),
            
            ListTile(
              leading: const Icon(Icons.help, color: AppColors.primary),
              title: const Text('Centro de ayuda'),
              subtitle: const Text('Preguntas frecuentes y guías'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _openHelp,
            ),
            
            ListTile(
              leading: const Icon(Icons.info, color: AppColors.primary),
              title: const Text('Acerca de'),
              subtitle: const Text('Información de la aplicación'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _showAbout,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Cuenta',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            ListTile(
              leading: const Icon(Icons.link_off, color: AppColors.warning),
              title: const Text('Desconectar pareja'),
              subtitle: const Text('Terminar conexión actual'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _disconnectCouple,
            ),
            
            ListTile(
              leading: const Icon(Icons.logout, color: AppColors.error),
              title: const Text('Cerrar sesión'),
              subtitle: const Text('Salir de la aplicación'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _signOut,
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final months = [
      'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
      'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
    ];
    return '${date.day} de ${months[date.month - 1]} de ${date.year}';
  }

  void _editProfile() {
    // Implementar edición de perfil
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Función de editar perfil próximamente'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _openSuggestions() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SuggestionsScreen(),
      ),
    );
  }

  void _openHelp() {
    // Implementar centro de ayuda
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Centro de ayuda próximamente'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _showAbout() {
    showAboutDialog(
      context: context,
      applicationName: 'Hopie',
      applicationVersion: '1.0.0',
      applicationIcon: Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(32),
        ),
        child: const Icon(
          Icons.favorite,
          color: AppColors.white,
          size: 32,
        ),
      ),
      children: [
        const Text(
          'Hopie es una aplicación diseñada para ayudar a las parejas a crecer juntas, '
          'documentar sus momentos especiales y fortalecer su relación día a día.',
        ),
      ],
    );
  }

  void _disconnectCouple() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Desconectar pareja'),
        content: const Text(
          '¿Estás seguro de que quieres desconectarte de tu pareja? '
          'Esto eliminará toda la información compartida.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Implementar desconexión
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Función próximamente'),
                  backgroundColor: AppColors.warning,
                ),
              );
            },
            child: const Text(
              'Desconectar',
              style: TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }

  void _signOut() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cerrar sesión'),
        content: const Text('¿Estás seguro de que quieres cerrar sesión?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<AuthProvider>().signOut();
            },
            child: const Text(
              'Cerrar sesión',
              style: TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }
}
