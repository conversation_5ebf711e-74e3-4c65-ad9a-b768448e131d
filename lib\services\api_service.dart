import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/models.dart';

class ApiService {
  static const String _baseUrl = 'YOUR_API_GATEWAY_URL'; // Reemplazar con tu URL de API Gateway
  static String? _authToken;

  // Headers comunes para todas las peticiones
  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    if (_authToken != null) 'Authorization': 'Bearer $_authToken',
  };

  // Configurar token de autenticación
  void setAuthToken(String token) {
    _authToken = token;
  }

  // Limpiar token de autenticación
  void clearAuthToken() {
    _authToken = null;
  }

  // Manejo de errores HTTP
  void _handleHttpError(http.Response response) {
    if (response.statusCode >= 400) {
      final errorBody = json.decode(response.body);
      throw ApiException(
        statusCode: response.statusCode,
        message: errorBody['message'] ?? 'Error desconocido',
      );
    }
  }

  // ============ USUARIOS ============

  Future<User> createUser(User user) async {
    final response = await http.post(
      Uri.parse('$_baseUrl/users'),
      headers: _headers,
      body: json.encode(user.toJson()),
    );

    _handleHttpError(response);
    return User.fromJson(json.decode(response.body));
  }

  Future<User> getUser(String userId) async {
    final response = await http.get(
      Uri.parse('$_baseUrl/users/$userId'),
      headers: _headers,
    );

    _handleHttpError(response);
    return User.fromJson(json.decode(response.body));
  }

  Future<User> updateUser(User user) async {
    final response = await http.put(
      Uri.parse('$_baseUrl/users/${user.userId}'),
      headers: _headers,
      body: json.encode(user.toJson()),
    );

    _handleHttpError(response);
    return User.fromJson(json.decode(response.body));
  }

  // ============ PAREJAS ============

  Future<Couple> createCouple(String user1Id, String user2Id, String treeType) async {
    final response = await http.post(
      Uri.parse('$_baseUrl/couples'),
      headers: _headers,
      body: json.encode({
        'user1Id': user1Id,
        'user2Id': user2Id,
        'treeType': treeType,
      }),
    );

    _handleHttpError(response);
    return Couple.fromJson(json.decode(response.body));
  }

  Future<Couple?> getCoupleByUserId(String userId) async {
    final response = await http.get(
      Uri.parse('$_baseUrl/users/$userId/couple'),
      headers: _headers,
    );

    if (response.statusCode == 404) return null;
    _handleHttpError(response);
    return Couple.fromJson(json.decode(response.body));
  }

  Future<User> connectWithPartner(String connectionCode) async {
    final response = await http.post(
      Uri.parse('$_baseUrl/couples/connect'),
      headers: _headers,
      body: json.encode({'connectionCode': connectionCode}),
    );

    _handleHttpError(response);
    return User.fromJson(json.decode(response.body));
  }

  // ============ ÁRBOL Y PROGRESO ============

  Future<TreeProgress> getTreeProgress(String coupleId) async {
    final response = await http.get(
      Uri.parse('$_baseUrl/couples/$coupleId/tree'),
      headers: _headers,
    );

    _handleHttpError(response);
    return TreeProgress.fromJson(json.decode(response.body));
  }

  Future<WateringRecord> waterTree(String coupleId) async {
    final response = await http.post(
      Uri.parse('$_baseUrl/couples/$coupleId/water'),
      headers: _headers,
    );

    _handleHttpError(response);
    return WateringRecord.fromJson(json.decode(response.body));
  }

  Future<List<WateringRecord>> getWateringHistory(String coupleId) async {
    final response = await http.get(
      Uri.parse('$_baseUrl/couples/$coupleId/watering-history'),
      headers: _headers,
    );

    _handleHttpError(response);
    final List<dynamic> data = json.decode(response.body);
    return data.map((item) => WateringRecord.fromJson(item)).toList();
  }

  // ============ PLAN DE VIDA ============

  Future<List<LifePlan>> getLifePlans(String coupleId) async {
    final response = await http.get(
      Uri.parse('$_baseUrl/couples/$coupleId/life-plans'),
      headers: _headers,
    );

    _handleHttpError(response);
    final List<dynamic> data = json.decode(response.body);
    return data.map((item) => LifePlan.fromJson(item)).toList();
  }

  Future<LifePlan> createLifePlan(LifePlan lifePlan) async {
    final response = await http.post(
      Uri.parse('$_baseUrl/couples/${lifePlan.coupleId}/life-plans'),
      headers: _headers,
      body: json.encode(lifePlan.toJson()),
    );

    _handleHttpError(response);
    return LifePlan.fromJson(json.decode(response.body));
  }

  Future<LifePlan> updateLifePlan(LifePlan lifePlan) async {
    final response = await http.put(
      Uri.parse('$_baseUrl/couples/${lifePlan.coupleId}/life-plans/${lifePlan.id}'),
      headers: _headers,
      body: json.encode(lifePlan.toJson()),
    );

    _handleHttpError(response);
    return LifePlan.fromJson(json.decode(response.body));
  }

  Future<void> deleteLifePlan(String coupleId, String lifePlanId) async {
    final response = await http.delete(
      Uri.parse('$_baseUrl/couples/$coupleId/life-plans/$lifePlanId'),
      headers: _headers,
    );

    _handleHttpError(response);
  }

  // ============ LUGARES ESPECIALES ============

  Future<List<SpecialPlace>> getSpecialPlaces(String coupleId) async {
    final response = await http.get(
      Uri.parse('$_baseUrl/couples/$coupleId/places'),
      headers: _headers,
    );

    _handleHttpError(response);
    final List<dynamic> data = json.decode(response.body);
    return data.map((item) => SpecialPlace.fromJson(item)).toList();
  }

  Future<SpecialPlace> createSpecialPlace(SpecialPlace place) async {
    final response = await http.post(
      Uri.parse('$_baseUrl/couples/${place.coupleId}/places'),
      headers: _headers,
      body: json.encode(place.toJson()),
    );

    _handleHttpError(response);
    return SpecialPlace.fromJson(json.decode(response.body));
  }

  Future<SpecialPlace> updateSpecialPlace(SpecialPlace place) async {
    final response = await http.put(
      Uri.parse('$_baseUrl/couples/${place.coupleId}/places/${place.id}'),
      headers: _headers,
      body: json.encode(place.toJson()),
    );

    _handleHttpError(response);
    return SpecialPlace.fromJson(json.decode(response.body));
  }

  Future<void> deleteSpecialPlace(String coupleId, String placeId) async {
    final response = await http.delete(
      Uri.parse('$_baseUrl/couples/$coupleId/places/$placeId'),
      headers: _headers,
    );

    _handleHttpError(response);
  }

  // ============ CHAT ============

  Future<List<ChatMessage>> getChatMessages(String coupleId, {int limit = 50}) async {
    final response = await http.get(
      Uri.parse('$_baseUrl/couples/$coupleId/messages?limit=$limit'),
      headers: _headers,
    );

    _handleHttpError(response);
    final List<dynamic> data = json.decode(response.body);
    return data.map((item) => ChatMessage.fromJson(item)).toList();
  }

  Future<ChatMessage> sendMessage(ChatMessage message) async {
    final response = await http.post(
      Uri.parse('$_baseUrl/couples/${message.coupleId}/messages'),
      headers: _headers,
      body: json.encode(message.toJson()),
    );

    _handleHttpError(response);
    return ChatMessage.fromJson(json.decode(response.body));
  }

  Future<void> markMessageAsRead(String coupleId, String messageId) async {
    final response = await http.put(
      Uri.parse('$_baseUrl/couples/$coupleId/messages/$messageId/read'),
      headers: _headers,
    );

    _handleHttpError(response);
  }

  // ============ PREGUNTAS ============

  Future<Question?> getTodaysQuestion(String coupleId) async {
    final response = await http.get(
      Uri.parse('$_baseUrl/couples/$coupleId/questions/today'),
      headers: _headers,
    );

    if (response.statusCode == 404) return null;
    _handleHttpError(response);
    return Question.fromJson(json.decode(response.body));
  }

  Future<Question> answerQuestion(String questionId, String userId, String answer) async {
    final response = await http.post(
      Uri.parse('$_baseUrl/questions/$questionId/answers'),
      headers: _headers,
      body: json.encode({
        'userId': userId,
        'answer': answer,
      }),
    );

    _handleHttpError(response);
    return Question.fromJson(json.decode(response.body));
  }

  // ============ UBICACIÓN ============

  Future<void> updateLocation(String userId, double lat, double lng) async {
    final response = await http.put(
      Uri.parse('$_baseUrl/users/$userId/location'),
      headers: _headers,
      body: json.encode({
        'lat': lat,
        'long': lng,
        'timestamp': DateTime.now().toIso8601String(),
      }),
    );

    _handleHttpError(response);
  }

  Future<List<UserLocation>> getPartnerLocations(String coupleId) async {
    final response = await http.get(
      Uri.parse('$_baseUrl/couples/$coupleId/locations'),
      headers: _headers,
    );

    _handleHttpError(response);
    final List<dynamic> data = json.decode(response.body);
    return data.map((item) => UserLocation.fromJson(item)).toList();
  }

  // ============ SUGERENCIAS ============

  Future<Suggestion> createSuggestion(String userId, String text) async {
    final response = await http.post(
      Uri.parse('$_baseUrl/suggestions'),
      headers: _headers,
      body: json.encode({
        'userId': userId,
        'text': text,
      }),
    );

    _handleHttpError(response);
    return Suggestion.fromJson(json.decode(response.body));
  }

  Future<List<Suggestion>> getUserSuggestions(String userId) async {
    final response = await http.get(
      Uri.parse('$_baseUrl/users/$userId/suggestions'),
      headers: _headers,
    );

    _handleHttpError(response);
    final List<dynamic> data = json.decode(response.body);
    return data.map((item) => Suggestion.fromJson(item)).toList();
  }
}

class ApiException implements Exception {
  final int statusCode;
  final String message;

  ApiException({required this.statusCode, required this.message});

  @override
  String toString() => 'ApiException: $statusCode - $message';
}
