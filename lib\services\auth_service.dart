import 'package:google_sign_in/google_sign_in.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/models.dart';
import 'api_service.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: ['email', 'profile'],
  );

  final ApiService _apiService = ApiService();
  User? _currentUser;
  String? _authToken;

  User? get currentUser => _currentUser;
  bool get isAuthenticated => _currentUser != null && _authToken != null;

  // Inicializar el servicio de autenticación
  Future<void> initialize() async {
    await _loadStoredAuth();
  }

  // Cargar autenticación almacenada
  Future<void> _loadStoredAuth() async {
    final prefs = await SharedPreferences.getInstance();
    final storedToken = prefs.getString('auth_token');
    final storedUserJson = prefs.getString('current_user');

    if (storedToken != null && storedUserJson != null) {
      _authToken = storedToken;
      _apiService.setAuthToken(storedToken);
      
      try {
        // Intentar cargar el usuario desde el almacenamiento local
        // En una implementación real, deberías validar el token con el servidor
        _currentUser = User.fromJson({
          'userId': prefs.getString('user_id') ?? '',
          'email': prefs.getString('user_email') ?? '',
          'name': prefs.getString('user_name') ?? '',
          'profilePic': prefs.getString('user_profile_pic') ?? '',
          'connectionCode': prefs.getString('user_connection_code') ?? '',
          'createdAt': prefs.getString('user_created_at') ?? DateTime.now().toIso8601String(),
          'coupleId': prefs.getString('user_couple_id'),
        });
      } catch (e) {
        // Si hay error al cargar, limpiar datos
        await _clearStoredAuth();
      }
    }
  }

  // Guardar autenticación en almacenamiento local
  Future<void> _saveAuth(User user, String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('auth_token', token);
    await prefs.setString('user_id', user.userId);
    await prefs.setString('user_email', user.email);
    await prefs.setString('user_name', user.name);
    await prefs.setString('user_profile_pic', user.profilePic);
    await prefs.setString('user_connection_code', user.connectionCode);
    await prefs.setString('user_created_at', user.createdAt.toIso8601String());
    if (user.coupleId != null) {
      await prefs.setString('user_couple_id', user.coupleId!);
    }
  }

  // Limpiar autenticación almacenada
  Future<void> _clearStoredAuth() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('auth_token');
    await prefs.remove('user_id');
    await prefs.remove('user_email');
    await prefs.remove('user_name');
    await prefs.remove('user_profile_pic');
    await prefs.remove('user_connection_code');
    await prefs.remove('user_created_at');
    await prefs.remove('user_couple_id');
  }

  // Iniciar sesión con Google
  Future<User> signInWithGoogle() async {
    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        throw AuthException('Inicio de sesión cancelado');
      }

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      
      // En una implementación real, enviarías el token de Google a tu backend
      // para validarlo con AWS Cognito y obtener un token JWT
      
      // Por ahora, simulamos la creación/obtención del usuario
      final user = User(
        userId: 'USER_${googleUser.id}',
        email: googleUser.email,
        name: googleUser.displayName ?? '',
        profilePic: googleUser.photoUrl ?? '',
        connectionCode: _generateConnectionCode(),
        createdAt: DateTime.now(),
      );

      // Simular token JWT (en implementación real vendría del backend)
      final token = 'jwt_token_${googleUser.id}';

      _currentUser = user;
      _authToken = token;
      _apiService.setAuthToken(token);
      
      await _saveAuth(user, token);

      return user;
    } catch (e) {
      throw AuthException('Error al iniciar sesión: $e');
    }
  }

  // Cerrar sesión
  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
      _currentUser = null;
      _authToken = null;
      _apiService.clearAuthToken();
      await _clearStoredAuth();
    } catch (e) {
      throw AuthException('Error al cerrar sesión: $e');
    }
  }

  // Actualizar usuario actual
  Future<void> updateCurrentUser(User user) async {
    _currentUser = user;
    if (_authToken != null) {
      await _saveAuth(user, _authToken!);
    }
  }

  // Generar código de conexión único
  String _generateConnectionCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    String code = '';
    
    for (int i = 0; i < 6; i++) {
      code += chars[(random + i) % chars.length];
    }
    
    return code;
  }

  // Conectar con pareja usando código
  Future<User> connectWithPartner(String connectionCode) async {
    try {
      final partner = await _apiService.connectWithPartner(connectionCode);
      
      // Actualizar usuario actual con información de pareja
      if (_currentUser != null) {
        final updatedUser = _currentUser!.copyWith(coupleId: 'COUPLE_ID');
        await updateCurrentUser(updatedUser);
      }
      
      return partner;
    } catch (e) {
      throw AuthException('Error al conectar con pareja: $e');
    }
  }

  // Verificar si el usuario tiene pareja
  bool get hasPartner => _currentUser?.coupleId != null;

  // Obtener información de la pareja
  Future<Couple?> getCurrentCouple() async {
    if (_currentUser?.userId == null) return null;
    
    try {
      return await _apiService.getCoupleByUserId(_currentUser!.userId);
    } catch (e) {
      return null;
    }
  }
}

class AuthException implements Exception {
  final String message;
  AuthException(this.message);

  @override
  String toString() => 'AuthException: $message';
}
