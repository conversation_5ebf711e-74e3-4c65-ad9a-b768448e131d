import 'package:flutter/material.dart';

class AppColors {
  // Paleta de colores principal basada en los colores especificados
  static const Color primary = Color(0xFFB4D5E5);      // #b4d5e5
  static const Color secondary = Color(0xFFCAD8EB);     // #cad8eb
  static const Color accent = Color(0xFFBCC3E5);       // #bcc3e5
  static const Color tertiary = Color(0xFFABCEDE);     // #abcede
  static const Color quaternary = Color(0xFFC5EBE4);   // #c5ebe4

  // Variaciones de los colores principales
  static const Color primaryLight = Color(0xFFD4E8F2);
  static const Color primaryDark = Color(0xFF94C2D8);
  
  static const Color secondaryLight = Color(0xFFE2EAF5);
  static const Color secondaryDark = Color(0xFFB0C6DE);

  // Colores de estado
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  // Colores neutros
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color grey = Color(0xFF9E9E9E);
  static const Color greyLight = Color(0xFFF5F5F5);
  static const Color greyDark = Color(0xFF424242);

  // Colores de texto
  static const Color textPrimary = Color(0xFF2C3E50);
  static const Color textSecondary = Color(0xFF7F8C8D);
  static const Color textLight = Color(0xFFBDC3C7);

  // Colores de fondo
  static const Color background = Color(0xFFF8FAFB);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF1F3F4);

  // Gradientes
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primary, secondary],
  );

  static const LinearGradient accentGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [accent, tertiary],
  );

  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [primaryLight, background],
  );

  // Colores específicos para el árbol
  static const Color treeSeed = Color(0xFF8D6E63);
  static const Color treeSprout = Color(0xFF66BB6A);
  static const Color treeSapling = Color(0xFF4CAF50);
  static const Color treeYoung = Color(0xFF388E3C);
  static const Color treeMature = Color(0xFF2E7D32);
  static const Color treeBlossomed = Color(0xFFE91E63);

  // Método para obtener color del árbol según la etapa
  static Color getTreeColor(String stage) {
    switch (stage.toLowerCase()) {
      case 'semilla':
        return treeSeed;
      case 'brote':
        return treeSprout;
      case 'plantula':
        return treeSapling;
      case 'arboljoven':
        return treeYoung;
      case 'arbolmaduro':
        return treeMature;
      case 'arbolflorecido':
        return treeBlossomed;
      default:
        return treeSeed;
    }
  }

  // Método para obtener el tema de colores de Material Design
  static ColorScheme get colorScheme => ColorScheme.fromSeed(
    seedColor: primary,
    brightness: Brightness.light,
    primary: primary,
    secondary: secondary,
    tertiary: accent,
    surface: surface,
    background: background,
    error: error,
  );

  // Método para obtener el tema oscuro
  static ColorScheme get darkColorScheme => ColorScheme.fromSeed(
    seedColor: primary,
    brightness: Brightness.dark,
    primary: primaryDark,
    secondary: secondaryDark,
    tertiary: accent,
    error: error,
  );
}
