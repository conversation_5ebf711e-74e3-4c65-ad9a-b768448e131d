import 'package:flutter/material.dart';

class AppColors {
  // Nueva paleta de colores más pálidos y cálidos
  static const Color primary = Color(0xFF8FA8C7);      // Jelly Bean Blue más pálido
  static const Color secondary = Color(0xFFA8B5AA);     // Xanadu más pálido
  static const Color accent = Color(0xFFB5C48A);       // Moss Green más pálido
  static const Color tertiary = Color(0xFFE8D5C4);     // Dark Vanilla más pálido
  static const Color quaternary = Color(0xFFF2F1EF);   // Platinum más pálido

  // Variaciones de los colores principales más cálidas
  static const Color primaryLight = Color(0xFFB8C8D9);
  static const Color primaryDark = Color(0xFF7A93B5);

  static const Color secondaryLight = Color(0xFFC2CAC4);
  static const Color secondaryDark = Color(0xFF8FA095);

  // Colores cálidos adicionales
  static const Color warmBeige = Color(0xFFF5F0E8);    // Beige cálido
  static const Color softPeach = Color(0xFFFFE5D9);    // Durazno suave
  static const Color paleGold = Color(0xFFFFF8DC);     // Oro pálido

  // Colores de estado
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  // Colores neutros
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color charcoal = Color(0xFF36454F);     // Charcoal
  static const Color grey = Color(0xFF9E9E9E);
  static const Color greyLight = Color(0xFFF5F5F5);
  static const Color greyDark = Color(0xFF424242);

  // Colores de texto más suaves y cálidos
  static const Color textPrimary = Color(0xFF4A4A4A);  // Gris cálido para texto principal
  static const Color textSecondary = Color(0xFF8A8A8A); // Gris medio para texto secundario
  static const Color textLight = Color(0xFFB5B5B5);    // Gris claro para texto suave

  // Colores de fondo cálidos
  static const Color background = warmBeige;           // Beige cálido como fondo principal
  static const Color surface = white;                  // Blanco para superficies
  static const Color surfaceVariant = paleGold;       // Oro pálido para variantes

  // Gradientes cálidos y suaves
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primary, primaryLight], // Azul pálido gradient
  );

  static const LinearGradient accentGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [accent, secondary], // Verde pálido a Xanadu
  );

  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [softPeach, warmBeige], // Durazno suave a beige cálido
  );

  static const LinearGradient warmGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [tertiary, softPeach], // Vanilla a durazno
  );

  // Colores específicos para el árbol basados en la nueva paleta
  static const Color treeSeed = charcoal;              // Charcoal para semilla
  static const Color treeSprout = Color(0xFF6B8A47);   // Moss Green más claro
  static const Color treeSapling = accent;             // Moss Green
  static const Color treeYoung = Color(0xFF7A8A4F);    // Moss Green intermedio
  static const Color treeMature = Color(0xFF6B7A43);   // Moss Green oscuro
  static const Color treeBlossomed = primary;          // Jelly Bean Blue para florecido

  // Método para obtener color del árbol según la etapa
  static Color getTreeColor(String stage) {
    switch (stage.toLowerCase()) {
      case 'semilla':
        return treeSeed;
      case 'brote':
        return treeSprout;
      case 'plantula':
        return treeSapling;
      case 'arboljoven':
        return treeYoung;
      case 'arbolmaduro':
        return treeMature;
      case 'arbolflorecido':
        return treeBlossomed;
      default:
        return treeSeed;
    }
  }

  // Método para obtener el tema de colores de Material Design
  static ColorScheme get colorScheme => ColorScheme.fromSeed(
    seedColor: primary,
    brightness: Brightness.light,
    primary: primary,
    secondary: secondary,
    tertiary: accent,
    surface: surface,
    error: error,
  );

  // Método para obtener el tema oscuro
  static ColorScheme get darkColorScheme => ColorScheme.fromSeed(
    seedColor: primary,
    brightness: Brightness.dark,
    primary: primaryDark,
    secondary: secondaryDark,
    tertiary: accent,
    error: error,
  );
}
