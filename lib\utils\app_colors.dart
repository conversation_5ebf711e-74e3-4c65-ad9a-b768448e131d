import 'package:flutter/material.dart';

class AppColors {
  // Nueva paleta de colores principal
  static const Color primary = Color(0xFF44749D);      // Jelly Bean Blue
  static const Color secondary = Color(0xFF738678);     // Xanadu
  static const Color accent = Color(0xFF8A9A5B);       // Moss Green
  static const Color tertiary = Color(0xFFD1BEA8);     // Dark Vanilla
  static const Color quaternary = Color(0xFFE5E4E2);   // Platinum

  // Variaciones de los colores principales
  static const Color primaryLight = Color(0xFF6B8FB8);
  static const Color primaryDark = Color(0xFF2D5A82);

  static const Color secondaryLight = Color(0xFF9BA49C);
  static const Color secondaryDark = Color(0xFF5A6B5F);

  // Colores de estado
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  // Colores neutros
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color charcoal = Color(0xFF36454F);     // Charcoal
  static const Color grey = Color(0xFF9E9E9E);
  static const Color greyLight = Color(0xFFF5F5F5);
  static const Color greyDark = Color(0xFF424242);

  // Colores de texto basados en la nueva paleta
  static const Color textPrimary = charcoal;           // Charcoal para texto principal
  static const Color textSecondary = Color(0xFF5A6B5F); // Xanadu oscuro para texto secundario
  static const Color textLight = Color(0xFF9BA49C);    // Xanadu claro para texto suave

  // Colores de fondo basados en la nueva paleta
  static const Color background = quaternary;          // Platinum como fondo principal
  static const Color surface = white;                  // Blanco para superficies
  static const Color surfaceVariant = Color(0xFFF0EFED); // Platinum más claro

  // Gradientes con la nueva paleta
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primary, primaryLight], // Jelly Bean Blue gradient
  );

  static const LinearGradient accentGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [accent, secondary], // Moss Green to Xanadu
  );

  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [tertiary, background], // Dark Vanilla to Platinum
  );

  // Colores específicos para el árbol basados en la nueva paleta
  static const Color treeSeed = charcoal;              // Charcoal para semilla
  static const Color treeSprout = Color(0xFF6B8A47);   // Moss Green más claro
  static const Color treeSapling = accent;             // Moss Green
  static const Color treeYoung = Color(0xFF7A8A4F);    // Moss Green intermedio
  static const Color treeMature = Color(0xFF6B7A43);   // Moss Green oscuro
  static const Color treeBlossomed = primary;          // Jelly Bean Blue para florecido

  // Método para obtener color del árbol según la etapa
  static Color getTreeColor(String stage) {
    switch (stage.toLowerCase()) {
      case 'semilla':
        return treeSeed;
      case 'brote':
        return treeSprout;
      case 'plantula':
        return treeSapling;
      case 'arboljoven':
        return treeYoung;
      case 'arbolmaduro':
        return treeMature;
      case 'arbolflorecido':
        return treeBlossomed;
      default:
        return treeSeed;
    }
  }

  // Método para obtener el tema de colores de Material Design
  static ColorScheme get colorScheme => ColorScheme.fromSeed(
    seedColor: primary,
    brightness: Brightness.light,
    primary: primary,
    secondary: secondary,
    tertiary: accent,
    surface: surface,
    error: error,
  );

  // Método para obtener el tema oscuro
  static ColorScheme get darkColorScheme => ColorScheme.fromSeed(
    seedColor: primary,
    brightness: Brightness.dark,
    primary: primaryDark,
    secondary: secondaryDark,
    tertiary: accent,
    error: error,
  );
}
