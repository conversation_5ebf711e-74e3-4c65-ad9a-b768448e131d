import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../utils/app_colors.dart';
import '../models/models.dart';

class DailyQuestionCard extends StatefulWidget {
  const DailyQuestionCard({super.key});

  @override
  State<DailyQuestionCard> createState() => _DailyQuestionCardState();
}

class _DailyQuestionCardState extends State<DailyQuestionCard> {
  final TextEditingController _answerController = TextEditingController();
  bool _isExpanded = false;
  
  // Pregunta de ejemplo (en implementación real vendría de la API)
  final Question _sampleQuestion = Question(
    id: 'q1',
    questionText: '¿Cuál fue el momento más especial que vivieron juntos esta semana?',
    category: QuestionCategory.daily,
    date: DateTime.now(),
    coupleId: 'couple1',
    answers: [],
  );

  @override
  void dispose() {
    _answerController.dispose();
    super.dispose();
  }

  void _submitAnswer() {
    final answer = _answerController.text.trim();
    if (answer.isEmpty) return;

    // Aquí implementarías el envío de la respuesta a la API
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Respuesta enviada exitosamente'),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
      ),
    );

    _answerController.clear();
    setState(() {
      _isExpanded = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.accent.withOpacity(0.1),
              AppColors.tertiary.withOpacity(0.1),
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.accent,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.quiz,
                      color: AppColors.white,
                      size: 20,
                    ),
                  ),
                  
                  const SizedBox(width: 12),
                  
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Pregunta del día',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        Text(
                          'Conecten más profundamente',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _isExpanded = !_isExpanded;
                      });
                    },
                    icon: Icon(
                      _isExpanded ? Icons.expand_less : Icons.expand_more,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Pregunta
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppColors.accent.withOpacity(0.3),
                  ),
                ),
                child: Text(
                  _sampleQuestion.questionText,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.textPrimary,
                    height: 1.4,
                  ),
                ),
              ),
              
              // Sección expandible
              AnimatedCrossFade(
                firstChild: const SizedBox.shrink(),
                secondChild: _buildExpandedSection(),
                crossFadeState: _isExpanded 
                    ? CrossFadeState.showSecond 
                    : CrossFadeState.showFirst,
                duration: const Duration(milliseconds: 300),
              ),
              
              // Estado de respuestas
              if (!_isExpanded) _buildAnswerStatus(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExpandedSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        
        // Campo de respuesta
        TextField(
          controller: _answerController,
          maxLines: 3,
          decoration: const InputDecoration(
            labelText: 'Tu respuesta',
            hintText: 'Comparte tus pensamientos...',
            border: OutlineInputBorder(),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Botones de acción
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: () {
                setState(() {
                  _isExpanded = false;
                });
              },
              child: const Text('Cancelar'),
            ),
            
            const SizedBox(width: 8),
            
            ElevatedButton(
              onPressed: _answerController.text.trim().isNotEmpty 
                  ? _submitAnswer 
                  : null,
              child: const Text('Enviar'),
            ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        // Respuestas existentes
        _buildExistingAnswers(),
      ],
    );
  }

  Widget _buildAnswerStatus() {
    return Padding(
      padding: const EdgeInsets.only(top: 12),
      child: Row(
        children: [
          _buildAnswerIndicator('Tú', false),
          const SizedBox(width: 16),
          _buildAnswerIndicator('Tu pareja', false),
          
          const Spacer(),
          
          TextButton(
            onPressed: () {
              setState(() {
                _isExpanded = true;
              });
            },
            child: const Text('Responder'),
          ),
        ],
      ),
    );
  }

  Widget _buildAnswerIndicator(String person, bool hasAnswered) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          hasAnswered ? Icons.check_circle : Icons.radio_button_unchecked,
          size: 16,
          color: hasAnswered ? AppColors.success : AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Text(
          person,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: hasAnswered ? AppColors.success : AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildExistingAnswers() {
    if (_sampleQuestion.answers.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Divider(),
        
        Text(
          'Respuestas',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 8),
        
        ..._sampleQuestion.answers.map((answer) => _buildAnswerItem(answer)),
      ],
    );
  }

  Widget _buildAnswerItem(QuestionAnswer answer) {
    final authProvider = context.read<AuthProvider>();
    final isCurrentUser = answer.userId == authProvider.currentUser?.userId;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isCurrentUser 
            ? AppColors.primary.withOpacity(0.1)
            : AppColors.greyLight,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                isCurrentUser ? 'Tú' : 'Tu pareja',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: isCurrentUser ? AppColors.primary : AppColors.textSecondary,
                ),
              ),
              const Spacer(),
              Text(
                _formatTime(answer.timestamp),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textLight,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 4),
          
          Text(
            answer.text,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inMinutes < 1) {
      return 'Ahora';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h';
    } else {
      return '${difference.inDays}d';
    }
  }
}
