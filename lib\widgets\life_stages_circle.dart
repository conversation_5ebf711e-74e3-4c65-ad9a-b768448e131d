import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../models/life_plan.dart';
import '../utils/app_colors.dart';

class LifeStagesCircle extends StatefulWidget {
  final List<LifePlan> lifePlans;
  final Function(LifePlan)? onStageSelected;
  final double size;

  const LifeStagesCircle({
    super.key,
    required this.lifePlans,
    this.onStageSelected,
    this.size = 300,
  });

  @override
  State<LifeStagesCircle> createState() => _LifeStagesCircleState();
}

class _LifeStagesCircleState extends State<LifeStagesCircle>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    _rotationController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
    
    _scaleController.forward();
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.lifePlans.isEmpty) {
      return _buildEmptyCircle();
    }

    return AnimatedBuilder(
      animation: Listenable.merge([_rotationAnimation, _scaleAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: SizedBox(
            width: widget.size,
            height: widget.size,
            child: Stack(
              children: [
                // Círculo base con gradiente
                _buildBaseCircle(),
                
                // Flechas direccionales
                _buildDirectionalArrows(),
                
                // Segmentos de etapas
                ..._buildStageSegments(),
                
                // Centro del círculo
                _buildCenterCircle(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyCircle() {
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: AppColors.textSecondary.withValues(alpha: 0.3),
          width: 2,
        ),
        gradient: RadialGradient(
          colors: [
            AppColors.primaryLight.withValues(alpha: 0.1),
            AppColors.primary.withValues(alpha: 0.05),
          ],
        ),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.timeline,
              size: 40,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: 8),
            Text(
              'Sin etapas',
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBaseCircle() {
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            AppColors.primaryLight.withValues(alpha: 0.1),
            AppColors.primary.withValues(alpha: 0.05),
            Colors.transparent,
          ],
          stops: const [0.0, 0.7, 1.0],
        ),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
          width: 2,
        ),
      ),
    );
  }

  Widget _buildDirectionalArrows() {
    return Transform.rotate(
      angle: _rotationAnimation.value * 0.1, // Rotación muy lenta
      child: CustomPaint(
        size: Size(widget.size, widget.size),
        painter: ArrowsPainter(
          stageCount: widget.lifePlans.length,
        ),
      ),
    );
  }

  List<Widget> _buildStageSegments() {
    final segments = <Widget>[];
    final stageCount = widget.lifePlans.length;
    final anglePerStage = 2 * math.pi / stageCount;

    for (int i = 0; i < stageCount; i++) {
      final stage = widget.lifePlans[i];
      final angle = i * anglePerStage - math.pi / 2; // Empezar desde arriba
      
      segments.add(
        _buildStageSegment(
          stage: stage,
          angle: angle,
          angleSpan: anglePerStage,
          index: i,
        ),
      );
    }

    return segments;
  }

  Widget _buildStageSegment({
    required LifePlan stage,
    required double angle,
    required double angleSpan,
    required int index,
  }) {
    final radius = widget.size / 2 - 40;
    final centerX = widget.size / 2;
    final centerY = widget.size / 2;
    
    // Calcular posición del texto
    final textAngle = angle + angleSpan / 2;
    final textX = centerX + radius * math.cos(textAngle);
    final textY = centerY + radius * math.sin(textAngle);

    return Positioned(
      left: textX - 60,
      top: textY - 30,
      child: GestureDetector(
        onTap: () => widget.onStageSelected?.call(stage),
        child: Container(
          width: 120,
          height: 60,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Indicador de estado
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: stage.completed 
                      ? AppColors.success 
                      : AppColors.primary,
                  border: Border.all(
                    color: AppColors.white,
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: (stage.completed ? AppColors.success : AppColors.primary)
                          .withValues(alpha: 0.3),
                      blurRadius: 8,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: stage.completed
                    ? const Icon(
                        Icons.check,
                        color: AppColors.white,
                        size: 14,
                      )
                    : Center(
                        child: Text(
                          '${index + 1}',
                          style: const TextStyle(
                            color: AppColors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
              ),
              
              const SizedBox(height: 4),
              
              // Nombre de la etapa
              Text(
                stage.name,
                style: TextStyle(
                  color: AppColors.textPrimary,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  shadows: [
                    Shadow(
                      color: AppColors.white.withValues(alpha: 0.8),
                      blurRadius: 2,
                    ),
                  ],
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCenterCircle() {
    return Center(
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: RadialGradient(
            colors: [
              AppColors.primary,
              AppColors.primaryLight,
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withValues(alpha: 0.3),
              blurRadius: 15,
              spreadRadius: 5,
            ),
          ],
        ),
        child: const Icon(
          Icons.favorite,
          color: AppColors.white,
          size: 32,
        ),
      ),
    );
  }
}

class ArrowsPainter extends CustomPainter {
  final int stageCount;

  ArrowsPainter({required this.stageCount});

  @override
  void paint(Canvas canvas, Size size) {
    if (stageCount <= 1) return;

    final paint = Paint()
      ..color = AppColors.primary.withValues(alpha: 0.4)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 60;
    final anglePerStage = 2 * math.pi / stageCount;

    for (int i = 0; i < stageCount; i++) {
      final startAngle = i * anglePerStage - math.pi / 2;
      final endAngle = startAngle + anglePerStage * 0.8;
      
      final startX = center.dx + radius * math.cos(startAngle);
      final startY = center.dy + radius * math.sin(startAngle);
      final endX = center.dx + radius * math.cos(endAngle);
      final endY = center.dy + radius * math.sin(endAngle);

      // Dibujar flecha curva
      final path = Path();
      path.moveTo(startX, startY);
      
      // Crear curva
      final controlX = center.dx + (radius + 20) * math.cos(startAngle + anglePerStage * 0.4);
      final controlY = center.dy + (radius + 20) * math.sin(startAngle + anglePerStage * 0.4);
      path.quadraticBezierTo(controlX, controlY, endX, endY);
      
      canvas.drawPath(path, paint);
      
      // Dibujar punta de flecha
      _drawArrowHead(canvas, paint, endX, endY, endAngle);
    }
  }

  void _drawArrowHead(Canvas canvas, Paint paint, double x, double y, double angle) {
    final arrowLength = 8.0;
    final arrowAngle = math.pi / 6;
    
    final path = Path();
    path.moveTo(x, y);
    path.lineTo(
      x - arrowLength * math.cos(angle - arrowAngle),
      y - arrowLength * math.sin(angle - arrowAngle),
    );
    path.moveTo(x, y);
    path.lineTo(
      x - arrowLength * math.cos(angle + arrowAngle),
      y - arrowLength * math.sin(angle + arrowAngle),
    );
    
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
