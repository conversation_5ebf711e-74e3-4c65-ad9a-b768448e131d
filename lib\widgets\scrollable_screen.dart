import 'package:flutter/material.dart';

/// Widget base para crear pantallas con scroll automático
/// Previene overflow y asegura que el contenido sea scrolleable
class ScrollableScreen extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final ScrollPhysics? physics;
  final bool shrinkWrap;
  final ScrollController? controller;

  const ScrollableScreen({
    super.key,
    required this.child,
    this.padding,
    this.physics,
    this.shrinkWrap = false,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          controller: controller,
          physics: physics ?? const BouncingScrollPhysics(),
          padding: padding ?? const EdgeInsets.all(16.0),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: shrinkWrap ? 0 : constraints.maxHeight - (padding?.vertical ?? 32),
            ),
            child: IntrinsicHeight(
              child: child,
            ),
          ),
        );
      },
    );
  }
}

/// Widget para contenido que necesita ocupar toda la altura disponible
class FullHeightScrollableScreen extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final ScrollPhysics? physics;
  final ScrollController? controller;

  const FullHeightScrollableScreen({
    super.key,
    required this.child,
    this.padding,
    this.physics,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          controller: controller,
          physics: physics ?? const BouncingScrollPhysics(),
          child: Container(
            constraints: BoxConstraints(
              minHeight: constraints.maxHeight,
            ),
            padding: padding ?? const EdgeInsets.all(16.0),
            child: child,
          ),
        );
      },
    );
  }
}

/// Widget para listas scrolleables con RefreshIndicator
class RefreshableScrollableScreen extends StatelessWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final EdgeInsetsGeometry? padding;
  final ScrollPhysics? physics;
  final ScrollController? controller;

  const RefreshableScrollableScreen({
    super.key,
    required this.child,
    required this.onRefresh,
    this.padding,
    this.physics,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: onRefresh,
      child: LayoutBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
            controller: controller,
            physics: physics ?? const AlwaysScrollableScrollPhysics(),
            padding: padding ?? const EdgeInsets.all(16.0),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: constraints.maxHeight - (padding?.vertical ?? 32),
              ),
              child: IntrinsicHeight(
                child: child,
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Widget para pantallas con AppBar y contenido scrolleable
class ScaffoldWithScrollableBody extends StatelessWidget {
  final Widget body;
  final PreferredSizeWidget? appBar;
  final Widget? floatingActionButton;
  final Widget? bottomNavigationBar;
  final Widget? drawer;
  final Widget? endDrawer;
  final Color? backgroundColor;
  final EdgeInsetsGeometry? padding;
  final ScrollPhysics? physics;
  final ScrollController? controller;
  final Future<void> Function()? onRefresh;

  const ScaffoldWithScrollableBody({
    super.key,
    required this.body,
    this.appBar,
    this.floatingActionButton,
    this.bottomNavigationBar,
    this.drawer,
    this.endDrawer,
    this.backgroundColor,
    this.padding,
    this.physics,
    this.controller,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    Widget scrollableBody;

    if (onRefresh != null) {
      scrollableBody = RefreshableScrollableScreen(
        onRefresh: onRefresh!,
        padding: padding,
        physics: physics,
        controller: controller,
        child: body,
      );
    } else {
      scrollableBody = ScrollableScreen(
        padding: padding,
        physics: physics,
        controller: controller,
        child: body,
      );
    }

    return Scaffold(
      appBar: appBar,
      body: scrollableBody,
      floatingActionButton: floatingActionButton,
      bottomNavigationBar: bottomNavigationBar,
      drawer: drawer,
      endDrawer: endDrawer,
      backgroundColor: backgroundColor,
    );
  }
}
