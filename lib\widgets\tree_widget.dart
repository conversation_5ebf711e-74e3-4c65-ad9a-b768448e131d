import 'package:flutter/material.dart';
import '../models/models.dart';
import '../utils/app_colors.dart';

class TreeWidget extends StatefulWidget {
  final TreeStage stage;
  final String treeType;
  final double size;

  const TreeWidget({
    super.key,
    required this.stage,
    required this.treeType,
    this.size = 200,
  });

  @override
  State<TreeWidget> createState() => _TreeWidgetState();
}

class _TreeWidgetState extends State<TreeWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: -0.05,
      end: 0.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: Container(
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(widget.size / 2),
                gradient: RadialGradient(
                  colors: [
                    AppColors.primaryLight.withOpacity(0.3),
                    Colors.transparent,
                  ],
                ),
              ),
              child: CustomPaint(
                painter: TreePainter(
                  stage: widget.stage,
                  treeType: widget.treeType,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class TreePainter extends CustomPainter {
  final TreeStage stage;
  final String treeType;

  TreePainter({
    required this.stage,
    required this.treeType,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    
    // Dibujar suelo
    _drawGround(canvas, size);
    
    // Dibujar árbol según la etapa
    switch (stage) {
      case TreeStage.semilla:
        _drawSeed(canvas, center);
        break;
      case TreeStage.brote:
        _drawSprout(canvas, center);
        break;
      case TreeStage.plantula:
        _drawSapling(canvas, center);
        break;
      case TreeStage.arbolJoven:
        _drawYoungTree(canvas, center, size);
        break;
      case TreeStage.arbolMaduro:
        _drawMatureTree(canvas, center, size);
        break;
      case TreeStage.arbolFlorecido:
        _drawBlossomedTree(canvas, center, size);
        break;
    }
  }

  void _drawGround(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF8D6E63)
      ..style = PaintingStyle.fill;

    final groundRect = Rect.fromLTWH(
      0,
      size.height * 0.8,
      size.width,
      size.height * 0.2,
    );

    canvas.drawRect(groundRect, paint);
  }

  void _drawSeed(Canvas canvas, Offset center) {
    final paint = Paint()
      ..color = const Color(0xFF5D4037)
      ..style = PaintingStyle.fill;

    canvas.drawCircle(
      Offset(center.dx, center.dy + 20),
      8,
      paint,
    );
  }

  void _drawSprout(Canvas canvas, Offset center) {
    // Semilla
    _drawSeed(canvas, center);
    
    // Brote verde
    final paint = Paint()
      ..color = const Color(0xFF66BB6A)
      ..style = PaintingStyle.fill
      ..strokeWidth = 3
      ..strokeCap = StrokeCap.round;

    canvas.drawLine(
      Offset(center.dx, center.dy + 20),
      Offset(center.dx, center.dy - 10),
      paint,
    );

    // Pequeñas hojas
    canvas.drawCircle(Offset(center.dx - 5, center.dy - 5), 3, paint);
    canvas.drawCircle(Offset(center.dx + 5, center.dy - 5), 3, paint);
  }

  void _drawSapling(Canvas canvas, Offset center) {
    final trunkPaint = Paint()
      ..color = const Color(0xFF8D6E63)
      ..style = PaintingStyle.fill
      ..strokeWidth = 4
      ..strokeCap = StrokeCap.round;

    final leafPaint = Paint()
      ..color = const Color(0xFF4CAF50)
      ..style = PaintingStyle.fill;

    // Tronco
    canvas.drawLine(
      Offset(center.dx, center.dy + 20),
      Offset(center.dx, center.dy - 30),
      trunkPaint,
    );

    // Hojas
    canvas.drawCircle(Offset(center.dx, center.dy - 30), 15, leafPaint);
  }

  void _drawYoungTree(Canvas canvas, Offset center, Size size) {
    final trunkPaint = Paint()
      ..color = const Color(0xFF8D6E63)
      ..style = PaintingStyle.fill
      ..strokeWidth = 8
      ..strokeCap = StrokeCap.round;

    final leafPaint = Paint()
      ..color = const Color(0xFF4CAF50)
      ..style = PaintingStyle.fill;

    // Tronco principal
    canvas.drawLine(
      Offset(center.dx, center.dy + 40),
      Offset(center.dx, center.dy - 40),
      trunkPaint,
    );

    // Ramas
    canvas.drawLine(
      Offset(center.dx, center.dy - 20),
      Offset(center.dx - 20, center.dy - 35),
      trunkPaint,
    );
    canvas.drawLine(
      Offset(center.dx, center.dy - 20),
      Offset(center.dx + 20, center.dy - 35),
      trunkPaint,
    );

    // Copa del árbol
    canvas.drawCircle(Offset(center.dx, center.dy - 40), 25, leafPaint);
    canvas.drawCircle(Offset(center.dx - 20, center.dy - 35), 15, leafPaint);
    canvas.drawCircle(Offset(center.dx + 20, center.dy - 35), 15, leafPaint);
  }

  void _drawMatureTree(Canvas canvas, Offset center, Size size) {
    final trunkPaint = Paint()
      ..color = const Color(0xFF6D4C41)
      ..style = PaintingStyle.fill
      ..strokeWidth = 12
      ..strokeCap = StrokeCap.round;

    final leafPaint = Paint()
      ..color = const Color(0xFF388E3C)
      ..style = PaintingStyle.fill;

    // Tronco principal
    canvas.drawLine(
      Offset(center.dx, center.dy + 50),
      Offset(center.dx, center.dy - 50),
      trunkPaint,
    );

    // Ramas principales
    final branchPaint = Paint()
      ..color = const Color(0xFF8D6E63)
      ..style = PaintingStyle.fill
      ..strokeWidth = 6
      ..strokeCap = StrokeCap.round;

    canvas.drawLine(
      Offset(center.dx, center.dy - 30),
      Offset(center.dx - 30, center.dy - 50),
      branchPaint,
    );
    canvas.drawLine(
      Offset(center.dx, center.dy - 30),
      Offset(center.dx + 30, center.dy - 50),
      branchPaint,
    );
    canvas.drawLine(
      Offset(center.dx, center.dy - 10),
      Offset(center.dx - 25, center.dy - 25),
      branchPaint,
    );
    canvas.drawLine(
      Offset(center.dx, center.dy - 10),
      Offset(center.dx + 25, center.dy - 25),
      branchPaint,
    );

    // Copa frondosa
    canvas.drawCircle(Offset(center.dx, center.dy - 50), 35, leafPaint);
    canvas.drawCircle(Offset(center.dx - 30, center.dy - 50), 20, leafPaint);
    canvas.drawCircle(Offset(center.dx + 30, center.dy - 50), 20, leafPaint);
    canvas.drawCircle(Offset(center.dx - 25, center.dy - 25), 18, leafPaint);
    canvas.drawCircle(Offset(center.dx + 25, center.dy - 25), 18, leafPaint);
  }

  void _drawBlossomedTree(Canvas canvas, Offset center, Size size) {
    // Dibujar árbol maduro como base
    _drawMatureTree(canvas, center, size);

    // Agregar flores
    final flowerPaint = Paint()
      ..color = const Color(0xFFE91E63)
      ..style = PaintingStyle.fill;

    final flowerPositions = [
      Offset(center.dx - 10, center.dy - 60),
      Offset(center.dx + 15, center.dy - 55),
      Offset(center.dx - 25, center.dy - 45),
      Offset(center.dx + 20, center.dy - 40),
      Offset(center.dx - 15, center.dy - 30),
      Offset(center.dx + 10, center.dy - 25),
    ];

    for (final position in flowerPositions) {
      canvas.drawCircle(position, 4, flowerPaint);
    }

    // Agregar brillo especial
    final glowPaint = Paint()
      ..color = const Color(0xFFE91E63).withOpacity(0.3)
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, size.width * 0.4, glowPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is TreePainter &&
        (oldDelegate.stage != stage || oldDelegate.treeType != treeType);
  }
}
